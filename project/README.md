# 🌟 GlowRoya - Persian Skincare E-commerce Platform

A comprehensive e-commerce platform specifically designed for Persian/Farsi skincare products with full RTL support, modern UI/UX, and robust backend infrastructure.

## ✨ Features

### 🛍️ Customer Features
- **Persian/RTL Interface** - Full right-to-left language support
- **Product Catalog** - Advanced filtering, search, and categorization
- **Shopping Cart** - Persistent cart with real-time updates
- **User Authentication** - Secure registration and login system
- **Order Management** - Complete order tracking and history
- **Wishlist** - Save favorite products for later
- **Reviews & Ratings** - Customer feedback system
- **Loyalty Program** - Points-based rewards system

### 👨‍💼 Admin Features
- **Dashboard** - Comprehensive analytics and metrics
- **Product Management** - CRUD operations for products, categories, brands
- **Order Management** - Order processing and fulfillment
- **User Management** - Customer and admin account management
- **Content Management** - Dynamic content and page management
- **Notification System** - Automated customer communications

### 🔧 Technical Features
- **Modern Stack** - React 18, TypeScript, Node.js, PostgreSQL
- **Responsive Design** - Mobile-first approach with Tailwind CSS
- **Real-time Updates** - Live notifications and cart synchronization
- **Security** - JWT authentication, input validation, rate limiting
- **Performance** - Optimized loading, caching, and image optimization
- **Scalability** - Microservices architecture with Docker deployment

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ and npm
- Docker and Docker Compose
- PostgreSQL client tools (for database operations)
- Access to remote VPS database (*************)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd glowroya
   ```

2. **Install dependencies**
   ```bash
   # Frontend dependencies
   npm install
   
   # Backend dependencies
   cd backend
   npm install
   cd ..
   ```

3. **Environment Configuration**
   ```bash
   # Copy environment template
   cp .env.example .env
   cp backend/.env.example backend/.env
   
   # Update environment variables with your configuration
   ```

4. **Database Setup**
   ```bash
   # Navigate to backend directory
   cd backend
   
   # Generate Prisma client
   npx prisma generate
   
   # Run database migrations
   npx prisma migrate deploy
   
   # Seed database with sample data
   npx prisma db seed
   ```

5. **Start Development Servers**
   ```bash
   # Start backend (from backend directory)
   npm run dev
   
   # Start frontend (from project root)
   npm run dev
   ```

### Production Deployment

1. **Using Docker Compose**
   ```bash
   # Build and deploy all services
   ./scripts/deploy.sh
   ```

2. **Manual Deployment**
   ```bash
   # Build frontend
   npm run build
   
   # Build backend
   cd backend
   npm run build
   
   # Start services
   docker-compose up -d
   ```

## 🏗️ Architecture

### Frontend (React + TypeScript)
- **Framework**: React 18 with TypeScript
- **Styling**: Tailwind CSS with RTL support
- **State Management**: Context API + Custom hooks
- **Routing**: React Router v6
- **Build Tool**: Vite
- **UI Components**: Custom component library

### Backend (Node.js + Express)
- **Runtime**: Node.js 18+
- **Framework**: Express.js with TypeScript
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: JWT with refresh tokens
- **Caching**: Redis for sessions and caching
- **File Upload**: Multer with image optimization
- **Logging**: Winston with daily rotation

### Database Schema
- **Users & Authentication** - User accounts, roles, sessions
- **Product Management** - Products, categories, brands, variants
- **Order Processing** - Orders, payments, fulfillment
- **Content Management** - Reviews, notifications, loyalty
- **System Management** - Logs, analytics, configurations

### Infrastructure
- **Containerization**: Docker with multi-stage builds
- **Reverse Proxy**: Nginx with SSL termination
- **Database**: Remote PostgreSQL on VPS (*************)
- **Caching**: Redis for performance optimization
- **Monitoring**: Health checks and logging

## 📊 Database Configuration

### Remote PostgreSQL Database
- **Host**: *************
- **Port**: 5432
- **Database**: glowroya
- **User**: remote_admin
- **Connection**: SSL enabled for security

### Database Management
```bash
# Connect to remote database
psql -h ************* -U remote_admin -d glowroya

# Run migrations
cd backend && npx prisma migrate deploy

# View database in Prisma Studio
npx prisma studio

# Create backup
./scripts/backup.sh
```

## 🔧 Development

### Project Structure
```
glowroya/
├── src/                    # Frontend source code
│   ├── components/         # React components
│   ├── pages/             # Page components
│   ├── hooks/             # Custom hooks
│   ├── contexts/          # React contexts
│   ├── services/          # API services
│   └── utils/             # Utility functions
├── backend/               # Backend source code
│   ├── src/               # TypeScript source
│   ├── prisma/            # Database schema and migrations
│   └── docker/            # Docker configurations
├── nginx/                 # Nginx configuration
├── scripts/               # Deployment and utility scripts
└── docker-compose.yml     # Docker services configuration
```

### Available Scripts

#### Frontend
- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint
- `npm run type-check` - TypeScript type checking

#### Backend
- `npm run dev` - Start development server with hot reload
- `npm run build` - Compile TypeScript to JavaScript
- `npm run start` - Start production server
- `npm run test` - Run test suite
- `npm run lint` - Run ESLint and fix issues

#### Database
- `npx prisma generate` - Generate Prisma client
- `npx prisma migrate dev` - Create and apply migration
- `npx prisma migrate deploy` - Apply migrations to production
- `npx prisma studio` - Open database GUI
- `npx prisma db seed` - Seed database with sample data

## 🔒 Security

### Authentication & Authorization
- JWT-based authentication with refresh tokens
- Role-based access control (Customer, Admin, Super Admin)
- Password hashing with bcrypt
- Session management with Redis

### Security Measures
- Input validation and sanitization
- Rate limiting to prevent abuse
- CORS configuration for cross-origin requests
- Security headers with Helmet.js
- SQL injection prevention with Prisma ORM

## 🌐 API Documentation

### Base URL
- Development: `http://localhost:3001/api/v1`
- Production: `https://yourdomain.com/api/v1`

### Key Endpoints
- `GET /health` - System health check
- `POST /auth/login` - User authentication
- `GET /products` - Product catalog
- `POST /orders` - Create new order
- `GET /users/profile` - User profile

### API Documentation
Visit `http://localhost:3001/api/v1/docs` for interactive API documentation.

## 🚀 Deployment

### Production Checklist
- [ ] Update environment variables
- [ ] Configure SSL certificates
- [ ] Set up database backups
- [ ] Configure monitoring and logging
- [ ] Test all critical functionality
- [ ] Set up CI/CD pipeline

### Monitoring & Maintenance
- Health checks at `/health` endpoint
- Automated backups with `./scripts/backup.sh`
- Log rotation and monitoring
- Performance metrics and analytics

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Check the documentation
- Review the API documentation

---

**GlowRoya** - Empowering Persian skincare businesses with modern e-commerce technology 🌟
