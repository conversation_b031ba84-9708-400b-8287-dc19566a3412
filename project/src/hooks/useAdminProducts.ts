import { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import {
  AdminProduct,
  ProductFormData,
  AdminProductFilters,
  BulkProductOperation,
  ProductValidationResult,
  ProductAnalytics,
  InventoryHistoryEntry,
  PriceHistoryEntry
} from '../types/adminProduct';
import { Product } from '../types';
import { validateProductForm, generateUniqueSku, generateSlug } from '../utils/productValidation';
import { useAdminAuth } from './useAdminAuth';
import AdminProductService from '../services/adminProductService';
import { ApiService } from '../services/apiService';
import toast from 'react-hot-toast';

// Convert regular product to admin product
const convertToAdminProduct = (product: Product): AdminProduct => {
  const now = new Date().toISOString();
  return {
    ...product,
    // Ensure all required fields have proper defaults
    brand: product.brand || '',
    benefits: product.benefits || [],
    ingredients: product.ingredients || [],
    howToUse: product.howToUse || [],
    size: product.size || '',
    weight: product.weight || '',
    images: product.images || [],
    variants: product.variants || [],
    hasVariants: product.hasVariants || false,

    // Admin-specific fields
    sku: `PROD-${product.id}`,
    barcode: '',
    status: 'active',
    visibility: 'visible',
    featured: product.isBestSeller || false,
    seoTitle: product.name,
    seoDescription: product.description.substring(0, 160),
    seoKeywords: [product.category, product.brand || '', 'مراقبت پوست'].filter(Boolean),
    slug: generateSlug(product.name),
    trackInventory: true,
    allowBackorder: false,
    lowStockThreshold: 10,
    costPrice: product.price * 0.6, // Mock cost price
    compareAtPrice: undefined,
    taxable: true,
    taxClass: '',
    requiresShipping: true,
    shippingWeight: 0.1,
    shippingDimensions: undefined,
    createdAt: now,
    updatedAt: now,
    publishedAt: now,
    createdBy: 'system',
    updatedBy: 'system',
    tags: [product.category, product.brand || ''].filter(Boolean),
    vendor: product.brand || '',
    productType: product.category,
    collections: [],
    priceHistory: [],
    inventoryHistory: []
  };
};

export const useAdminProducts = () => {
  const { user } = useAdminAuth();
  const [products, setProducts] = useState<AdminProduct[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<AdminProductFilters>({});
  const loadingRef = useRef(false);
  const hasLoadedRef = useRef(false);

  // Load products from backend API - only once on mount
  useEffect(() => {
    const loadProducts = async () => {
      // Prevent multiple simultaneous loads
      if (loadingRef.current || hasLoadedRef.current) {
        return;
      }

      try {
        loadingRef.current = true;
        setLoading(true);
        setError(null);

        // Load from API
        const response = await AdminProductService.getProducts({
          page: 1,
          limit: 100, // Load all products initially
        });

        setProducts(response.products);
        hasLoadedRef.current = true;
        toast.success('محصولات از API بارگذاری شد');
      } catch (err) {
        const errorMessage = ApiService.ErrorHandler.handleError(err as Error);
        setError(errorMessage);
        toast.error(errorMessage);

        // If everything fails, show empty state
        setProducts([]);
      } finally {
        setLoading(false);
        loadingRef.current = false;
      }
    };

    loadProducts();
  }, []); // Only load once on mount

  // No need to save to localStorage - data is managed by backend API

  // Filter products based on current filters
  const filteredProducts = useMemo(() => {
    let result = [...products];

    // Search filter
    if (filters.search) {
      const searchTerm = filters.search.toLowerCase();
      result = result.filter(product => 
        product.name.toLowerCase().includes(searchTerm) ||
        product.sku.toLowerCase().includes(searchTerm) ||
        product.description.toLowerCase().includes(searchTerm) ||
        product.brand?.toLowerCase().includes(searchTerm)
      );
    }

    // Status filter
    if (filters.status && filters.status.length > 0) {
      result = result.filter(product => filters.status!.includes(product.status));
    }

    // Visibility filter
    if (filters.visibility && filters.visibility.length > 0) {
      result = result.filter(product => filters.visibility!.includes(product.visibility));
    }

    // Category filter
    if (filters.category && filters.category.length > 0) {
      result = result.filter(product => filters.category!.includes(product.category));
    }

    // Brand filter
    if (filters.brand && filters.brand.length > 0) {
      result = result.filter(product => 
        product.brand && filters.brand!.includes(product.brand)
      );
    }

    // Featured filter
    if (filters.featured !== undefined) {
      result = result.filter(product => product.featured === filters.featured);
    }

    // New products filter
    if (filters.isNew !== undefined) {
      result = result.filter(product => product.isNew === filters.isNew);
    }

    // Best sellers filter
    if (filters.isBestSeller !== undefined) {
      result = result.filter(product => product.isBestSeller === filters.isBestSeller);
    }

    // Low stock filter
    if (filters.lowStock) {
      result = result.filter(product => 
        product.trackInventory && product.stock <= product.lowStockThreshold
      );
    }

    // Out of stock filter
    if (filters.outOfStock) {
      result = result.filter(product => product.stock === 0);
    }

    // Price range filter
    if (filters.priceRange) {
      const [min, max] = filters.priceRange;
      result = result.filter(product => 
        product.price >= min && product.price <= max
      );
    }

    // Date range filter
    if (filters.dateRange) {
      const { field, start, end } = filters.dateRange;
      result = result.filter(product => {
        const date = new Date(product[field] || '');
        return date >= new Date(start) && date <= new Date(end);
      });
    }

    // Tags filter
    if (filters.tags && filters.tags.length > 0) {
      result = result.filter(product =>
        filters.tags!.some(tag => product.tags.includes(tag))
      );
    }

    // Sorting
    if (filters.sortBy) {
      result.sort((a, b) => {
        let aValue: any = a[filters.sortBy as keyof AdminProduct];
        let bValue: any = b[filters.sortBy as keyof AdminProduct];

        // Handle different data types
        if (typeof aValue === 'string') {
          aValue = aValue.toLowerCase();
          bValue = bValue.toLowerCase();
        }

        if (aValue < bValue) return filters.sortOrder === 'desc' ? 1 : -1;
        if (aValue > bValue) return filters.sortOrder === 'desc' ? -1 : 1;
        return 0;
      });
    }

    return result;
  }, [products, filters]);

  // Get product by ID
  const getProduct = useCallback((id: number): AdminProduct | undefined => {
    return products.find(product => product.id === id);
  }, [products]);

  // Create new product
  const createProduct = useCallback(async (data: ProductFormData): Promise<AdminProduct> => {
    try {
      setLoading(true);
      setError(null);

      // Validate form data
      const existingSkus = products.map(p => p.sku);
      const validation = validateProductForm(data, existingSkus);

      if (!validation.isValid) {
        throw new Error(validation.errors[0].message);
      }

      // Prepare product data for API
      const productData = {
        name: data.name,
        description: data.description,
        shortDescription: data.description.substring(0, 160),
        sku: data.sku,
        barcode: data.barcode,
        price: data.price,
        comparePrice: data.compareAtPrice,
        costPrice: data.costPrice,
        weight: parseFloat(data.weight || '0'),
        isActive: data.status === 'active',
        isFeatured: data.featured,
        requiresShipping: data.requiresShipping,
        trackQuantity: data.trackInventory,
        allowBackorder: data.allowBackorder,
        metaTitle: data.seoTitle,
        metaDescription: data.seoDescription,
        tags: data.tags,
        stock: data.stock,
        lowStockThreshold: data.lowStockThreshold,
      };

      // Try to create product via API first
      try {
        const newProduct = await AdminProductService.createProduct(productData);
        setProducts(prev => [...prev, newProduct]);
        toast.success('محصول با موفقیت ایجاد شد');
        return newProduct;
      } catch (apiError) {
        // If API fails, create locally as fallback
        console.warn('API create failed, creating locally:', apiError);

        const newProduct: AdminProduct = {
          id: Date.now(), // Temporary ID
          name: data.name,
          description: data.description,
          price: data.price,
          stock: data.stock || 0,
          category: data.category || 'عمومی',
          rating: 0,
          reviewCount: 0,
          images: data.images || [],
          isNew: false,
          isBestSeller: false,
          ...productData,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          publishedAt: new Date().toISOString(),
          createdBy: user?.name || 'admin',
          updatedBy: user?.name || 'admin',
        } as AdminProduct;

        // Update local state
        setProducts(prev => [...prev, newProduct]);
        toast.success('محصول با موفقیت ایجاد شد (حالت آزمایشی)');
        return newProduct;
      }
    } catch (err) {
      const errorMessage = ApiService.ErrorHandler.handleError(err as Error);
      setError(errorMessage);
      toast.error(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [products]);

  // Update existing product
  const updateProduct = useCallback(async (id: number, data: Partial<ProductFormData>): Promise<AdminProduct> => {
    try {
      setLoading(true);
      setError(null);

      // Prepare update data for API
      const updateData: any = {};

      if (data.name) updateData.name = data.name;
      if (data.description) updateData.description = data.description;
      if (data.sku) updateData.sku = data.sku;
      if (data.barcode) updateData.barcode = data.barcode;
      if (data.price !== undefined) updateData.price = data.price;
      if (data.compareAtPrice !== undefined) updateData.comparePrice = data.compareAtPrice;
      if (data.costPrice !== undefined) updateData.costPrice = data.costPrice;
      if (data.weight) updateData.weight = parseFloat(data.weight);
      if (data.status) updateData.isActive = data.status === 'active';
      if (data.featured !== undefined) updateData.isFeatured = data.featured;
      if (data.requiresShipping !== undefined) updateData.requiresShipping = data.requiresShipping;
      if (data.trackInventory !== undefined) updateData.trackQuantity = data.trackInventory;
      if (data.allowBackorder !== undefined) updateData.allowBackorder = data.allowBackorder;
      if (data.seoTitle) updateData.metaTitle = data.seoTitle;
      if (data.seoDescription) updateData.metaDescription = data.seoDescription;
      if (data.tags) updateData.tags = data.tags;
      if (data.stock !== undefined) updateData.stock = data.stock;
      if (data.lowStockThreshold !== undefined) updateData.lowStockThreshold = data.lowStockThreshold;

      // Try to update product via API first
      try {
        const updatedProduct = await AdminProductService.updateProduct(String(id), updateData);
        setProducts(prev => prev.map(p => p.id === id ? updatedProduct : p));
        toast.success('محصول با موفقیت بروزرسانی شد');
        return updatedProduct;
      } catch (apiError) {
        // If API fails, update locally as fallback
        console.warn('API update failed, updating locally:', apiError);

        const existingProduct = products.find(p => p.id === id);
        if (!existingProduct) {
          throw new Error('محصول یافت نشد');
        }

        const updatedProduct: AdminProduct = {
          ...existingProduct,
          ...updateData,
          updatedAt: new Date().toISOString(),
          updatedBy: user?.name || 'admin',
        };

        // Update local state
        setProducts(prev => prev.map(p => p.id === id ? updatedProduct : p));
        toast.success('محصول با موفقیت بروزرسانی شد (حالت آزمایشی)');
        return updatedProduct;
      }
    } catch (err) {
      const errorMessage = ApiService.ErrorHandler.handleError(err as Error);
      setError(errorMessage);
      toast.error(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  // Delete product
  const deleteProduct = useCallback(async (id: number): Promise<void> => {
    try {
      setLoading(true);
      setError(null);

      // Try to delete product via API first
      try {
        await AdminProductService.deleteProduct(String(id));
        setProducts(prev => prev.filter(p => p.id !== id));
        toast.success('محصول با موفقیت حذف شد');
      } catch (apiError) {
        // If API fails, delete locally as fallback
        console.warn('API delete failed, deleting locally:', apiError);

        setProducts(prev => prev.filter(p => p.id !== id));
        toast.success('محصول با موفقیت حذف شد (حالت آزمایشی)');
      }
    } catch (err) {
      const errorMessage = ApiService.ErrorHandler.handleError(err as Error);
      setError(errorMessage);
      toast.error(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  // Bulk operations
  const bulkOperation = useCallback(async (operation: BulkProductOperation): Promise<void> => {
    try {
      setLoading(true);
      
      switch (operation.type) {
        case 'update_status':
          setProducts(prev => prev.map(p => 
            operation.productIds.includes(p.id) 
              ? { ...p, status: operation.data.status, updatedAt: new Date().toISOString() }
              : p
          ));
          break;
          
        case 'delete':
          setProducts(prev => prev.filter(p => !operation.productIds.includes(p.id)));
          break;
          
        // Add more bulk operations as needed
      }
      
      toast.success(`عملیات گروهی روی ${operation.productIds.length} محصول انجام شد`);
    } catch (err) {
      const message = 'خطا در انجام عملیات گروهی';
      setError(message);
      toast.error(message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  // Get analytics for a product
  const getProductAnalytics = useCallback((productId: number, days = 30): ProductAnalytics => {
    // Mock analytics data
    const product = getProduct(productId);
    if (!product) {
      throw new Error('محصول یافت نشد');
    }

    return {
      productId,
      views: Math.floor(Math.random() * 1000) + 100,
      sales: Math.floor(Math.random() * 50) + 10,
      revenue: product.price * (Math.floor(Math.random() * 50) + 10),
      conversionRate: Math.random() * 5 + 1,
      averageRating: product.rating,
      reviewCount: product.reviewCount,
      returnRate: Math.random() * 2,
      profitMargin: product.costPrice ? ((product.price - product.costPrice) / product.price) * 100 : 0,
      period: {
        start: new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString(),
        end: new Date().toISOString()
      }
    };
  }, [getProduct]);

  return {
    // State
    products: filteredProducts,
    allProducts: products,
    loading,
    error,
    filters,

    // Actions
    setFilters,
    getProduct,
    createProduct,
    updateProduct,
    deleteProduct,
    bulkOperation,
    getProductAnalytics,

    // Utilities
    clearError: () => setError(null)
  };
};
