import { useWishlist as useWishlistContext } from '../context/WishlistContext';
import { Product } from '../types';
import { WishlistPriority, WishlistFilters } from '../types/comparison';

// Re-export the context hook with additional utilities
export const useWishlist = () => {
  const context = useWishlistContext();

  // Additional utility functions
  const toggleWishlist = (product: Product, priority?: WishlistPriority) => {
    if (context.isInWishlist(product.id)) {
      const item = context.getWishlistItem(product.id);
      if (item) {
        context.removeItem(item.id);
      }
    } else {
      context.addItem(product, priority);
    }
  };

  const getWishlistStats = () => {
    const { items } = context;
    
    const totalValue = items.reduce((sum, item) => {
      const price = item.product.discountedPrice || item.product.price;
      return sum + price;
    }, 0);

    const categoryCounts = items.reduce((acc, item) => {
      const category = item.product.category;
      acc[category] = (acc[category] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const priorityCounts = items.reduce((acc, item) => {
      acc[item.priority] = (acc[item.priority] || 0) + 1;
      return acc;
    }, {} as Record<WishlistPriority, number>);

    const availableItems = items.filter(item => item.product.stock > 0).length;
    const outOfStockItems = items.filter(item => item.product.stock === 0).length;

    return {
      totalItems: items.length,
      totalValue,
      categoryCounts,
      priorityCounts,
      availableItems,
      outOfStockItems,
      averagePrice: items.length > 0 ? totalValue / items.length : 0
    };
  };

  const getRecommendationsBasedOnWishlist = (): Product[] => {
    // This would typically call an API or use a recommendation engine
    // For now, return empty array - can be implemented later
    return [];
  };

  const exportWishlist = () => {
    const { items } = context;
    const exportData = items.map(item => ({
      name: item.product.name,
      brand: item.product.brand,
      category: item.product.category,
      price: item.product.price,
      discountedPrice: item.product.discountedPrice,
      priority: item.priority,
      addedAt: item.addedAt.toISOString(),
      notes: item.notes || '',
      inStock: item.product.stock > 0
    }));

    const dataStr = JSON.stringify(exportData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `wishlist-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const bulkMoveToCart = (itemIds: string[]) => {
    const products: Product[] = [];
    itemIds.forEach(itemId => {
      const product = context.moveToCart(itemId);
      if (product) {
        products.push(product);
      }
    });
    return products;
  };

  const bulkRemove = (itemIds: string[]) => {
    itemIds.forEach(itemId => {
      context.removeItem(itemId);
    });
  };

  const updatePriority = (itemId: string, priority: WishlistPriority) => {
    context.updateItem(itemId, { priority });
  };

  const addNotes = (itemId: string, notes: string) => {
    context.updateItem(itemId, { notes });
  };

  const getFilterOptions = () => {
    const { items } = context;
    
    const categories = Array.from(new Set(items.map(item => item.product.category)));
    const priorities: WishlistPriority[] = ['urgent', 'high', 'medium', 'low'];
    
    const prices = items.map(item => item.product.discountedPrice || item.product.price);
    const minPrice = Math.min(...prices);
    const maxPrice = Math.max(...prices);

    return {
      categories,
      priorities,
      priceRange: prices.length > 0 ? [minPrice, maxPrice] as [number, number] : [0, 0] as [number, number]
    };
  };

  return {
    ...context,
    toggleWishlist,
    getWishlistStats,
    getRecommendationsBasedOnWishlist,
    exportWishlist,
    bulkMoveToCart,
    bulkRemove,
    updatePriority,
    addNotes,
    getFilterOptions
  };
};
