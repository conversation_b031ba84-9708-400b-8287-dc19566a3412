import React from 'react';
import { motion } from 'framer-motion';
import { Filter, X, RotateCcw, Star } from 'lucide-react';
import { FilterState, FilterConfig } from '../../types';
import CategoryFilter from './CategoryFilter';
import BrandFilter from './BrandFilter';
import PriceRangeFilter from './PriceRangeFilter';
import StockFilter from './StockFilter';

interface FilterSidebarProps {
  filters: FilterState;
  filterConfig: FilterConfig;
  onUpdateCategories: (categories: string[]) => void;
  onToggleCategory: (category: string) => void;
  onUpdateBrands: (brands: string[]) => void;
  onToggleBrand: (brand: string) => void;
  onUpdatePriceRange: (range: [number, number]) => void;
  onUpdateStockStatus: (status: FilterState['stockStatus']) => void;
  onUpdateProductTypes: (types: Partial<FilterState['productTypes']>) => void;
  onUpdateRating: (rating: number) => void;
  onClearFilters: () => void;
  onClearFilter: (filterType: keyof FilterState) => void;
  activeFilterCount: number;
  filterSummary: string[];
  isOpen?: boolean;
  onClose?: () => void;
  isMobile?: boolean;
}

const FilterSidebar: React.FC<FilterSidebarProps> = ({
  filters,
  filterConfig,
  onUpdateCategories,
  onToggleCategory,
  onUpdateBrands,
  onToggleBrand,
  onUpdatePriceRange,
  onUpdateStockStatus,
  onUpdateProductTypes,
  onUpdateRating,
  onClearFilters,
  onClearFilter,
  activeFilterCount,
  filterSummary,
  isOpen = true,
  onClose,
  isMobile = false
}) => {
  const hasActiveFilters = activeFilterCount > 0;

  const handleClearCategories = () => onClearFilter('categories');
  const handleClearBrands = () => onClearFilter('brands');
  const handleClearPriceRange = () => onClearFilter('priceRange');
  const handleClearStockFilters = () => {
    onClearFilter('stockStatus');
    onClearFilter('productTypes');
  };

  // Rating options
  const ratingOptions = [
    { value: 0, label: 'همه امتیازها' },
    { value: 4, label: '۴ ستاره و بالاتر' },
    { value: 3, label: '۳ ستاره و بالاتر' },
    { value: 2, label: '۲ ستاره و بالاتر' },
    { value: 1, label: '۱ ستاره و بالاتر' }
  ];

  const sidebarContent = (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-5 border-b border-gray-100">
        <div className="flex items-center gap-2">
          <Filter className="w-5 h-5 text-primary-500" />
          <h2 className="font-semibold text-lg text-text-primary">فیلترها</h2>
          {hasActiveFilters && (
            <span className="bg-primary-100 text-primary-700 text-xs px-2 py-1 rounded-full">
              {activeFilterCount}
            </span>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          {hasActiveFilters && (
            <button
              onClick={onClearFilters}
              className="flex items-center gap-1 text-sm text-text-muted hover:text-red-500 transition-colors"
              title="پاک کردن همه فیلترها"
            >
              <RotateCcw className="w-4 h-4" />
              پاک کردن همه
            </button>
          )}
          
          {isMobile && onClose && (
            <button
              onClick={onClose}
              className="p-1 rounded-full hover:bg-gray-100 transition-colors"
              aria-label="بستن فیلترها"
            >
              <X className="w-5 h-5 text-text-secondary" />
            </button>
          )}
        </div>
      </div>

      {/* Active Filters Summary */}
      {hasActiveFilters && (
        <div className="p-5 bg-primary-50 border-b border-primary-100">
          <h3 className="text-sm font-medium text-primary-700 mb-2">فیلترهای فعال:</h3>
          <div className="space-y-1">
            {filterSummary.map((summary, index) => (
              <div key={index} className="text-xs text-primary-600">
                {summary}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Filters Content */}
      <div className="flex-1 overflow-y-auto custom-scroll p-5 space-y-6">
        {/* Category Filter */}
        <CategoryFilter
          categories={filterConfig.categories}
          selectedCategories={filters.categories}
          onToggleCategory={onToggleCategory}
          onClearCategories={handleClearCategories}
        />

        {/* Brand Filter */}
        <BrandFilter
          brands={filterConfig.brands}
          selectedBrands={filters.brands}
          onToggleBrand={onToggleBrand}
          onClearBrands={handleClearBrands}
          showSearch={filterConfig.brands.length > 5}
        />

        {/* Price Range Filter */}
        <PriceRangeFilter
          priceRange={filterConfig.priceRange}
          selectedRange={filters.priceRange}
          onRangeChange={onUpdatePriceRange}
          onClearRange={handleClearPriceRange}
        />

        {/* Stock and Product Type Filter */}
        <StockFilter
          stockStatus={filters.stockStatus}
          onStockStatusChange={onUpdateStockStatus}
          productTypes={filters.productTypes}
          onProductTypesChange={onUpdateProductTypes}
          onClearStockFilters={handleClearStockFilters}
        />

        {/* Rating Filter */}
        <div className="border-b border-gray-100 pb-6 mb-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="font-medium text-text-primary">امتیاز محصولات</h3>
            {filters.rating > 0 && (
              <button
                onClick={() => onUpdateRating(0)}
                className="text-sm text-text-muted hover:text-red-500 transition-colors"
              >
                پاک کردن
              </button>
            )}
          </div>
          
          <div className="space-y-2">
            {ratingOptions.map((option, index) => (
              <motion.label
                key={option.value}
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.2, delay: index * 0.05 }}
                className="flex items-center cursor-pointer group"
              >
                <input
                  type="radio"
                  name="rating"
                  value={option.value}
                  checked={filters.rating === option.value}
                  onChange={() => onUpdateRating(option.value)}
                  className="sr-only"
                />
                <div className={`w-5 h-5 rounded-full border-2 transition-all duration-200 flex items-center justify-center ${
                  filters.rating === option.value
                    ? 'bg-primary-500 border-primary-500'
                    : 'border-gray-300 group-hover:border-primary-400'
                }`}>
                  {filters.rating === option.value && (
                    <div className="w-2 h-2 bg-white rounded-full" />
                  )}
                </div>
                <div className="mr-3 flex items-center gap-1">
                  <span className={`text-sm transition-colors ${
                    filters.rating === option.value 
                      ? 'text-primary-600 font-medium' 
                      : 'text-text-secondary group-hover:text-text-primary'
                  }`}>
                    {option.label}
                  </span>
                  {option.value > 0 && (
                    <div className="flex items-center">
                      {[...Array(5)].map((_, i) => (
                        <Star
                          key={i}
                          className={`w-3 h-3 ${
                            i < option.value 
                              ? 'text-accent-500 fill-current' 
                              : 'text-gray-300'
                          }`}
                        />
                      ))}
                    </div>
                  )}
                </div>
              </motion.label>
            ))}
          </div>
        </div>
      </div>

      {/* Footer */}
      {isMobile && (
        <div className="p-5 border-t border-gray-100 bg-white">
          <div className="flex gap-3">
            <button
              onClick={onClearFilters}
              className="flex-1 py-3 px-4 border border-gray-200 rounded-lg text-text-secondary hover:bg-gray-50 transition-colors"
              disabled={!hasActiveFilters}
            >
              پاک کردن همه
            </button>
            <button
              onClick={onClose}
              className="flex-1 py-3 px-4 bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-colors"
            >
              اعمال فیلترها
            </button>
          </div>
        </div>
      )}
    </div>
  );

  if (isMobile) {
    return (
      <>
        {/* Mobile Overlay */}
        {isOpen && (
          <div 
            className="fixed inset-0 bg-black/50 z-40"
            onClick={onClose}
          />
        )}
        
        {/* Mobile Sidebar */}
        <motion.div
          initial={{ x: '100%' }}
          animate={{ x: isOpen ? 0 : '100%' }}
          transition={{ type: 'tween', duration: 0.3 }}
          className="fixed top-0 right-0 h-full w-80 max-w-[90vw] bg-white z-50 shadow-xl"
        >
          {sidebarContent}
        </motion.div>
      </>
    );
  }

  // Desktop Sidebar
  return (
    <motion.aside
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.3 }}
      className="w-80 bg-white rounded-2xl shadow-soft h-fit sticky top-24"
    >
      {sidebarContent}
    </motion.aside>
  );
};

export default FilterSidebar;
