import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { ChevronDown, DollarSign } from 'lucide-react';
import { PriceRange } from '../../types';

interface PriceRangeFilterProps {
  priceRange: PriceRange;
  selectedRange: [number, number];
  onRangeChange: (range: [number, number]) => void;
  onClearRange: () => void;
  isCollapsible?: boolean;
  defaultExpanded?: boolean;
}

const PriceRangeFilter: React.FC<PriceRangeFilterProps> = ({
  priceRange,
  selectedRange,
  onRangeChange,
  onClearRange,
  isCollapsible = false,
  defaultExpanded = true
}) => {
  const [isExpanded, setIsExpanded] = useState(defaultExpanded);
  const [localRange, setLocalRange] = useState(selectedRange);
  const [isDragging, setIsDragging] = useState(false);

  // Update local range when selectedRange changes
  useEffect(() => {
    setLocalRange(selectedRange);
  }, [selectedRange]);

  // Debounced update to parent
  useEffect(() => {
    if (!isDragging) {
      const timeoutId = setTimeout(() => {
        if (localRange[0] !== selectedRange[0] || localRange[1] !== selectedRange[1]) {
          onRangeChange(localRange);
        }
      }, 300);
      return () => clearTimeout(timeoutId);
    }
  }, [localRange, selectedRange, onRangeChange, isDragging]);

  const hasCustomRange = selectedRange[0] !== priceRange.min || selectedRange[1] !== priceRange.max;

  const handleMinChange = (value: number) => {
    const newRange: [number, number] = [Math.min(value, localRange[1]), localRange[1]];
    setLocalRange(newRange);
  };

  const handleMaxChange = (value: number) => {
    const newRange: [number, number] = [localRange[0], Math.max(value, localRange[0])];
    setLocalRange(newRange);
  };

  const handleInputChange = (value: string, type: 'min' | 'max') => {
    const numValue = parseInt(value.replace(/,/g, '')) || 0;
    const clampedValue = Math.max(priceRange.min, Math.min(priceRange.max, numValue));
    
    if (type === 'min') {
      handleMinChange(clampedValue);
    } else {
      handleMaxChange(clampedValue);
    }
  };

  // Predefined price ranges
  const predefinedRanges = [
    { label: 'زیر ۱۰۰ هزار تومان', min: priceRange.min, max: 100000 },
    { label: '۱۰۰ تا ۲۰۰ هزار تومان', min: 100000, max: 200000 },
    { label: '۲۰۰ تا ۳۰۰ هزار تومان', min: 200000, max: 300000 },
    { label: '۳۰۰ تا ۵۰۰ هزار تومان', min: 300000, max: 500000 },
    { label: 'بالای ۵۰۰ هزار تومان', min: 500000, max: priceRange.max }
  ];

  return (
    <div className="border-b border-gray-100 pb-6 mb-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <button
          onClick={() => isCollapsible && setIsExpanded(!isExpanded)}
          className={`flex items-center gap-2 font-medium text-text-primary ${
            isCollapsible ? 'hover:text-primary-600 transition-colors' : ''
          }`}
        >
          <span>محدوده قیمت</span>
          {hasCustomRange && (
            <span className="bg-primary-100 text-primary-700 text-xs px-2 py-1 rounded-full">
              فعال
            </span>
          )}
          {isCollapsible && (
            <ChevronDown 
              className={`w-4 h-4 transition-transform ${isExpanded ? 'rotate-180' : ''}`} 
            />
          )}
        </button>
        
        {hasCustomRange && (
          <button
            onClick={onClearRange}
            className="text-sm text-text-muted hover:text-red-500 transition-colors"
          >
            پاک کردن
          </button>
        )}
      </div>

      {/* Content */}
      {(!isCollapsible || isExpanded) && (
        <motion.div
          initial={isCollapsible ? { height: 0, opacity: 0 } : false}
          animate={isCollapsible ? { height: 'auto', opacity: 1 } : false}
          exit={isCollapsible ? { height: 0, opacity: 0 } : false}
          transition={{ duration: 0.2 }}
          className="space-y-6"
        >
          {/* Current Range Display */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between text-sm text-text-secondary mb-2">
              <span>از</span>
              <span>تا</span>
            </div>
            <div className="flex items-center gap-3">
              <div className="flex-1 relative">
                <input
                  type="text"
                  value={localRange[0].toLocaleString()}
                  onChange={(e) => handleInputChange(e.target.value, 'min')}
                  className="w-full px-3 py-2 text-sm border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-center"
                />
                <span className="absolute left-2 top-1/2 -translate-y-1/2 text-xs text-text-muted">
                  تومان
                </span>
              </div>
              <span className="text-text-muted">-</span>
              <div className="flex-1 relative">
                <input
                  type="text"
                  value={localRange[1].toLocaleString()}
                  onChange={(e) => handleInputChange(e.target.value, 'max')}
                  className="w-full px-3 py-2 text-sm border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-center"
                />
                <span className="absolute left-2 top-1/2 -translate-y-1/2 text-xs text-text-muted">
                  تومان
                </span>
              </div>
            </div>
          </div>

          {/* Range Sliders */}
          <div className="space-y-4">
            <div className="relative">
              {/* Track */}
              <div className="h-2 bg-gray-200 rounded-full relative">
                <div 
                  className="h-2 bg-primary-500 rounded-full absolute"
                  style={{
                    right: `${((localRange[0] - priceRange.min) / (priceRange.max - priceRange.min)) * 100}%`,
                    width: `${((localRange[1] - localRange[0]) / (priceRange.max - priceRange.min)) * 100}%`
                  }}
                />
              </div>
              
              {/* Min Slider */}
              <input
                type="range"
                min={priceRange.min}
                max={priceRange.max}
                step={priceRange.step}
                value={localRange[0]}
                onChange={(e) => {
                  setIsDragging(true);
                  handleMinChange(parseInt(e.target.value));
                }}
                onMouseUp={() => setIsDragging(false)}
                onTouchEnd={() => setIsDragging(false)}
                className="absolute top-0 w-full h-2 opacity-0 cursor-pointer"
              />
              
              {/* Max Slider */}
              <input
                type="range"
                min={priceRange.min}
                max={priceRange.max}
                step={priceRange.step}
                value={localRange[1]}
                onChange={(e) => {
                  setIsDragging(true);
                  handleMaxChange(parseInt(e.target.value));
                }}
                onMouseUp={() => setIsDragging(false)}
                onTouchEnd={() => setIsDragging(false)}
                className="absolute top-0 w-full h-2 opacity-0 cursor-pointer"
              />
            </div>
            
            {/* Range Labels */}
            <div className="flex justify-between text-xs text-text-muted">
              <span>{priceRange.min.toLocaleString()} تومان</span>
              <span>{priceRange.max.toLocaleString()} تومان</span>
            </div>
          </div>

          {/* Predefined Ranges */}
          <div className="space-y-2">
            <h4 className="text-sm font-medium text-text-primary mb-3">محدوده‌های پیشنهادی</h4>
            {predefinedRanges.map((range, index) => {
              const isActive = localRange[0] === range.min && localRange[1] === range.max;
              
              return (
                <button
                  key={index}
                  onClick={() => setLocalRange([range.min, range.max])}
                  className={`w-full text-right px-3 py-2 text-sm rounded-lg transition-colors ${
                    isActive
                      ? 'bg-primary-100 text-primary-700 border border-primary-200'
                      : 'hover:bg-gray-50 text-text-secondary'
                  }`}
                >
                  {range.label}
                </button>
              );
            })}
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default PriceRangeFilter;
