import React, { useState, useEffect, useCallback } from 'react';
import { motion } from 'framer-motion';
import LazyImage from './LazyImage';
import { 
  generateOptimizedImageUrl,
  generateResponsiveImageUrls,
  getOptimalImageFormat,
  preloadImage,
  ImageOptimizationOptions,
  ResponsiveImageSizes,
  DEFAULT_RESPONSIVE_SIZES
} from '../../utils/imageOptimization';
import { useMobileDetection } from '../../hooks/useMobileDetection';

export interface ImageOptimizerProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  quality?: number;
  format?: 'auto' | 'webp' | 'jpeg' | 'png' | 'avif';
  responsive?: boolean;
  responsiveSizes?: Partial<ResponsiveImageSizes>;
  lazy?: boolean;
  preload?: boolean;
  placeholder?: 'blur' | 'color' | 'none';
  placeholderColor?: string;
  aspectRatio?: number;
  objectFit?: 'cover' | 'contain' | 'fill' | 'scale-down' | 'none';
  className?: string;
  containerClassName?: string;
  onLoad?: () => void;
  onError?: () => void;
  enableWebP?: boolean;
  enableAVIF?: boolean;
  progressive?: boolean;
  lossless?: boolean;
  blur?: number;
  sharpen?: number;
  brightness?: number;
  contrast?: number;
  saturation?: number;
  children?: React.ReactNode;
}

const ImageOptimizer: React.FC<ImageOptimizerProps> = ({
  src,
  alt,
  width,
  height,
  quality = 80,
  format = 'auto',
  responsive = true,
  responsiveSizes = {},
  lazy = true,
  preload = false,
  placeholder = 'blur',
  placeholderColor = '#f3f4f6',
  aspectRatio,
  objectFit = 'cover',
  className = '',
  containerClassName = '',
  onLoad,
  onError,
  enableWebP = true,
  enableAVIF = true,
  progressive = true,
  lossless = false,
  blur,
  sharpen,
  brightness,
  contrast,
  saturation,
  children
}) => {
  const { isMobile, isTablet, isDesktop } = useMobileDetection();
  const [optimizedSrc, setOptimizedSrc] = useState<string>('');
  const [isOptimizing, setIsOptimizing] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Merge responsive sizes with defaults
  const sizes = { ...DEFAULT_RESPONSIVE_SIZES, ...responsiveSizes };

  // Determine optimal format based on browser support and preferences
  const determineOptimalFormat = useCallback(async () => {
    if (format !== 'auto') return format;

    try {
      const optimalFormat = await getOptimalImageFormat('jpeg');
      return optimalFormat;
    } catch (error) {
      console.warn('Failed to determine optimal image format:', error);
      return 'jpeg';
    }
  }, [format]);

  // Get device-specific size
  const getDeviceSize = useCallback(() => {
    if (isMobile) return { width: sizes.mobile, height: undefined };
    if (isTablet) return { width: sizes.tablet, height: undefined };
    if (isDesktop) return { width: sizes.desktop, height: undefined };
    return { width: sizes.wide, height: undefined };
  }, [isMobile, isTablet, isDesktop, sizes]);

  // Generate optimization options
  const generateOptimizationOptions = useCallback(async (): Promise<ImageOptimizationOptions> => {
    const optimalFormat = await determineOptimalFormat();
    const deviceSize = getDeviceSize();

    return {
      quality,
      format: optimalFormat as any,
      width: width || deviceSize.width,
      height: height || deviceSize.height,
      progressive,
      lossless,
      blur,
    };
  }, [
    determineOptimalFormat,
    getDeviceSize,
    quality,
    width,
    height,
    progressive,
    lossless,
    blur
  ]);

  // Optimize image
  const optimizeImage = useCallback(async () => {
    setIsOptimizing(true);
    setError(null);

    try {
      const options = await generateOptimizationOptions();
      const optimized = generateOptimizedImageUrl(src, options);
      setOptimizedSrc(optimized);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Image optimization failed');
      setOptimizedSrc(src); // Fallback to original
    } finally {
      setIsOptimizing(false);
    }
  }, [src, generateOptimizationOptions]);

  // Preload image if requested
  const preloadOptimizedImage = useCallback(async () => {
    if (!preload || !optimizedSrc) return;

    try {
      await preloadImage(optimizedSrc);
    } catch (error) {
      console.warn('Failed to preload image:', error);
    }
  }, [preload, optimizedSrc]);

  // Initialize optimization
  useEffect(() => {
    optimizeImage();
  }, [src, width, height, quality, format]); // Only depend on actual values, not functions

  // Preload when optimized src is ready
  useEffect(() => {
    if (preload && optimizedSrc) {
      preloadOptimizedImage();
    }
  }, [preload, optimizedSrc]); // Only depend on actual values

  // Handle load event
  const handleLoad = useCallback(() => {
    onLoad?.();
  }, [onLoad]);

  // Handle error event
  const handleError = useCallback(() => {
    setError('Failed to load image');
    onError?.();
  }, [onError]);

  // Generate responsive image URLs if responsive is enabled
  const responsiveUrls = responsive ? generateResponsiveImageUrls(src, sizes, {
    quality,
    format: format === 'auto' ? 'webp' : format,
    progressive,
    lossless
  }) : null;

  // Render loading state
  if (isOptimizing) {
    return (
      <div className={`image-optimizer-loading ${containerClassName}`}>
        <div 
          className="flex items-center justify-center bg-gray-100 animate-pulse"
          style={{
            aspectRatio: aspectRatio?.toString(),
            width: width || '100%',
            height: height || 'auto'
          }}
        >
          <div className="text-gray-400">
            <svg className="w-8 h-8 animate-spin" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
            </svg>
          </div>
        </div>
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <div className={`image-optimizer-error ${containerClassName}`}>
        <div 
          className="flex items-center justify-center bg-red-50 text-red-500 border border-red-200"
          style={{
            aspectRatio: aspectRatio?.toString(),
            width: width || '100%',
            height: height || 'auto'
          }}
        >
          <div className="text-center">
            <div className="text-2xl mb-2">⚠️</div>
            <div className="text-sm">{error}</div>
          </div>
        </div>
      </div>
    );
  }

  // Render optimized image
  if (lazy) {
    return (
      <LazyImage
        src={optimizedSrc}
        alt={alt}
        className={className}
        containerClassName={containerClassName}
        width={width}
        height={height}
        aspectRatio={aspectRatio}
        objectFit={objectFit}
        onLoad={handleLoad}
        onError={handleError}
        showPlaceholder={placeholder !== 'none'}
        placeholderColor={placeholderColor}
        enableProgressiveLoading={progressive}
        enableResponsive={responsive}
        responsiveSizes={sizes}
        optimizationOptions={{
          quality,
          format: format === 'auto' ? 'webp' : format,
          progressive,
          lossless
        }}
      />
    );
  }

  // Render regular optimized image
  return (
    <div className={`image-optimizer ${containerClassName}`}>
      <motion.img
        src={optimizedSrc}
        alt={alt}
        className={className}
        width={width}
        height={height}
        style={{
          objectFit,
          aspectRatio: aspectRatio?.toString()
        }}
        onLoad={handleLoad}
        onError={handleError}
        initial={{ opacity: 0, scale: 1.05 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.3, ease: 'easeOut' }}
        {...(responsive && responsiveUrls && {
          srcSet: Object.entries(responsiveUrls)
            .map(([size, url]) => `${url} ${sizes[size as keyof ResponsiveImageSizes]}w`)
            .join(', '),
          sizes: `(max-width: 640px) ${sizes.mobile}px, (max-width: 768px) ${sizes.tablet}px, (max-width: 1024px) ${sizes.desktop}px, ${sizes.wide}px`
        })}
      />
      {children}
    </div>
  );
};

// Preset configurations for common use cases
export const OptimizedProductImage: React.FC<Omit<ImageOptimizerProps, 'quality' | 'format'>> = (props) => (
  <ImageOptimizer
    {...props}
    quality={85}
    format="auto"
    responsive={true}
    lazy={true}
    placeholder="blur"
    aspectRatio={1}
    objectFit="cover"
  />
);

export const OptimizedHeroImage: React.FC<Omit<ImageOptimizerProps, 'quality' | 'format'>> = (props) => (
  <ImageOptimizer
    {...props}
    quality={90}
    format="auto"
    responsive={true}
    lazy={false}
    preload={true}
    placeholder="blur"
    aspectRatio={16/9}
    objectFit="cover"
  />
);

export const OptimizedThumbnail: React.FC<Omit<ImageOptimizerProps, 'quality' | 'format'>> = (props) => (
  <ImageOptimizer
    {...props}
    quality={75}
    format="auto"
    responsive={false}
    lazy={true}
    placeholder="color"
    aspectRatio={1}
    objectFit="cover"
  />
);

export const OptimizedAvatar: React.FC<Omit<ImageOptimizerProps, 'quality' | 'format'>> = (props) => (
  <ImageOptimizer
    {...props}
    quality={80}
    format="auto"
    responsive={false}
    lazy={true}
    placeholder="color"
    aspectRatio={1}
    objectFit="cover"
    className={`rounded-full ${props.className || ''}`}
  />
);

export default ImageOptimizer;
