import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { Product } from '../types';
import {
  WishlistItem,
  WishlistPriority,
  WishlistFilters,
  WishlistContextType,
  PERSIAN_WISHLIST_MESSAGES
} from '../types/comparison';
import { useAuth } from './AuthContext';
import { useAdminAuth } from './AdminAuthContext';
import toast from 'react-hot-toast';

const WishlistContext = createContext<WishlistContextType | undefined>(undefined);

interface WishlistProviderProps {
  children: ReactNode;
}

// Storage utilities
const WISHLIST_STORAGE_KEY = 'glowroya_wishlist';

const saveWishlistToStorage = (items: WishlistItem[], userId?: string) => {
  const key = userId ? `${WISHLIST_STORAGE_KEY}_${userId}` : WISHLIST_STORAGE_KEY;
  localStorage.setItem(key, JSON.stringify(items));
};

const loadWishlistFromStorage = (userId?: string): WishlistItem[] => {
  try {
    const key = userId ? `${WISHLIST_STORAGE_KEY}_${userId}` : WISHLIST_STORAGE_KEY;
    const stored = localStorage.getItem(key);
    if (stored) {
      const items = JSON.parse(stored);
      // Convert date strings back to Date objects
      return items.map((item: any) => ({
        ...item,
        addedAt: new Date(item.addedAt)
      }));
    }
  } catch (error) {
    console.error('Error loading wishlist from storage:', error);
  }
  return [];
};

// Default filters
const getDefaultFilters = (): WishlistFilters => ({
  sortBy: 'date_added_desc'
});

export const WishlistProvider: React.FC<WishlistProviderProps> = ({ children }) => {
  const { user } = useAuth();
  const { user: adminUser } = useAdminAuth();

  const [items, setItems] = useState<WishlistItem[]>([]);
  const [filters, setFilters] = useState<WishlistFilters>(getDefaultFilters());
  const [isLoading, setIsLoading] = useState(false);

  // Use either regular user or admin user for wishlist storage
  const currentUser = user || adminUser;

  // Load wishlist from storage on mount and when user changes
  useEffect(() => {
    const stored = loadWishlistFromStorage(currentUser?.id);
    setItems(stored);
  }, [currentUser?.id]);

  // Save wishlist to storage when items change
  useEffect(() => {
    if (items.length >= 0) { // Save even empty wishlist to clear storage
      saveWishlistToStorage(items, currentUser?.id);
    }
  }, [items, currentUser?.id]);

  // Generate unique ID for wishlist items
  const generateWishlistItemId = (productId: string): string => {
    return `wishlist_${productId}_${Date.now()}`;
  };

  // Add item to wishlist
  const addItem = (product: Product, priority: WishlistPriority = 'medium', notes?: string) => {
    // Check if item already exists
    const existingItem = items.find(item => item.product.id === product.id);
    if (existingItem) {
      toast.error('این محصول قبلاً به علاقه‌مندی‌ها اضافه شده');
      return;
    }

    const newItem: WishlistItem = {
      id: generateWishlistItemId(product.id),
      product,
      addedAt: new Date(),
      notes,
      priority,
      category: product.category,
      tags: []
    };

    setItems(prev => [newItem, ...prev]);
    toast.success(PERSIAN_WISHLIST_MESSAGES.addedToWishlist);
  };

  // Remove item from wishlist
  const removeItem = (itemId: string) => {
    setItems(prev => prev.filter(item => item.id !== itemId));
    toast.success(PERSIAN_WISHLIST_MESSAGES.removedFromWishlist);
  };

  // Update wishlist item
  const updateItem = (itemId: string, updates: Partial<WishlistItem>) => {
    setItems(prev => prev.map(item => 
      item.id === itemId ? { ...item, ...updates } : item
    ));
  };

  // Clear entire wishlist
  const clearWishlist = () => {
    setItems([]);
    toast.success(PERSIAN_WISHLIST_MESSAGES.wishlistCleared);
  };

  // Check if product is in wishlist
  const isInWishlist = (productId: string): boolean => {
    return items.some(item => item.product.id === productId);
  };

  // Get wishlist item by product ID
  const getWishlistItem = (productId: string): WishlistItem | undefined => {
    return items.find(item => item.product.id === productId);
  };

  // Move item to cart (returns the product to be added to cart)
  const moveToCart = (itemId: string) => {
    const item = items.find(i => i.id === itemId);
    if (item) {
      removeItem(itemId);
      toast.success(PERSIAN_WISHLIST_MESSAGES.movedToCart);
      return item.product;
    }
    return null;
  };

  // Share wishlist
  const shareWishlist = () => {
    const shareText = `علاقه‌مندی‌های من در گلو رویا - ${items.length} محصول`;
    const shareUrl = `${window.location.origin}/wishlist`;
    
    if (navigator.share) {
      navigator.share({
        title: shareText,
        url: shareUrl
      });
    } else {
      navigator.clipboard.writeText(shareUrl);
      toast.success('لینک علاقه‌مندی‌ها کپی شد');
    }
  };

  // Apply filters and sorting
  const filteredItems = React.useMemo(() => {
    let filtered = [...items];

    // Apply category filter
    if (filters.category) {
      filtered = filtered.filter(item => item.product.category === filters.category);
    }

    // Apply priority filter
    if (filters.priority) {
      filtered = filtered.filter(item => item.priority === filters.priority);
    }

    // Apply price range filter
    if (filters.priceRange) {
      const [min, max] = filters.priceRange;
      filtered = filtered.filter(item => {
        const price = item.product.discountedPrice || item.product.price;
        return price >= min && price <= max;
      });
    }

    // Apply availability filter
    if (filters.availability && filters.availability !== 'all') {
      if (filters.availability === 'in_stock') {
        filtered = filtered.filter(item => item.product.stock > 0);
      } else if (filters.availability === 'out_of_stock') {
        filtered = filtered.filter(item => item.product.stock === 0);
      }
    }

    // Apply sorting
    filtered.sort((a, b) => {
      switch (filters.sortBy) {
        case 'date_added':
          return a.addedAt.getTime() - b.addedAt.getTime();
        case 'date_added_desc':
          return b.addedAt.getTime() - a.addedAt.getTime();
        case 'name':
          return a.product.name.localeCompare(b.product.name, 'fa');
        case 'name_desc':
          return b.product.name.localeCompare(a.product.name, 'fa');
        case 'price':
          const priceA = a.product.discountedPrice || a.product.price;
          const priceB = b.product.discountedPrice || b.product.price;
          return priceA - priceB;
        case 'price_desc':
          const priceA2 = a.product.discountedPrice || a.product.price;
          const priceB2 = b.product.discountedPrice || b.product.price;
          return priceB2 - priceA2;
        case 'priority':
          const priorityOrder = { urgent: 4, high: 3, medium: 2, low: 1 };
          return priorityOrder[b.priority] - priorityOrder[a.priority];
        default:
          return 0;
      }
    });

    return filtered;
  }, [items, filters]);

  const value: WishlistContextType = {
    items,
    totalItems: items.length,
    isLoading,
    addItem,
    removeItem,
    updateItem,
    clearWishlist,
    filters,
    setFilters: (newFilters) => setFilters(prev => ({ ...prev, ...newFilters })),
    filteredItems,
    isInWishlist,
    getWishlistItem,
    moveToCart,
    shareWishlist
  };

  return (
    <WishlistContext.Provider value={value}>
      {children}
    </WishlistContext.Provider>
  );
};

export const useWishlist = (): WishlistContextType => {
  const context = useContext(WishlistContext);
  if (!context) {
    throw new Error('useWishlist must be used within a WishlistProvider');
  }
  return context;
};
