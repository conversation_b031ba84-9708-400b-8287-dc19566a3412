import React, { useEffect, useState } from 'react';
import { usePara<PERSON>, Link, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { ChevronLeft, ArrowRight } from 'lucide-react';
import { getProductById } from '../data/products';
import { Product } from '../types';
import ProductGallery from '../components/product/ProductGallery';
import ProductInfo from '../components/product/ProductInfo';
import ProductSpecifications from '../components/product/ProductSpecifications';
import ProductReviews from '../components/product/ProductReviews';
import RelatedProducts from '../components/product/RelatedProducts';
import SEOHead from '../components/seo/SEOHead';
import StructuredData from '../components/seo/StructuredData';
import JsonLd from '../components/seo/JsonLd';
import SocialShare from '../components/social/SocialShare';
import GuaranteeInfo from '../components/trust/GuaranteeInfo';
import ReviewSystem from '../components/reviews/ReviewSystem';
import { generateProductMetaDescription, generateProductKeywords, generateBreadcrumbs, generateProductFAQs } from '../utils/seoUtils';
import { generateProductSocialTags, generateProductShareData } from '../utils/socialUtils';

const ProductDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [product, setProduct] = useState<Product | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (id) {
      const productId = parseInt(id, 10);
      const foundProduct = getProductById(productId);
      
      if (foundProduct) {
        setProduct(foundProduct);
      } else {
        // Product not found, redirect to products page
        navigate('/products');
        return;
      }
    }
    setLoading(false);
  }, [id, navigate]);

  useEffect(() => {
    // Scroll to top when component mounts
    window.scrollTo(0, 0);
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  if (!product) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-text-primary mb-4">
            محصول یافت نشد
          </h2>
          <Link to="/products" className="btn-primary">
            بازگشت به فروشگاه
          </Link>
        </div>
      </div>
    );
  }

  // Prepare images for gallery
  const galleryImages = product.images && product.images.length > 0 
    ? product.images 
    : [product.imageSrc];

  return (
    <div className="min-h-screen bg-gray-50">
      <SEOHead
        title={product.name}
        description={generateProductMetaDescription(product)}
        keywords={generateProductKeywords(product)}
        image={product.imageSrc}
        type="product"
        price={product.discountedPrice || product.price}
        currency="IRR"
        availability={product.stock > 0 ? 'in_stock' : 'out_of_stock'}
        brand={product.brand}
        category={product.category}
        canonicalUrl={`/product/${product.id}`}
      />

      <StructuredData
        type="product"
        data={{
          product,
          reviews: [] // This would come from actual reviews data
        }}
      />

      <JsonLd
        type="product"
        data={{
          product,
          reviews: [],
          gtin: product.id,
          priceValidUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          shippingCost: 0
        }}
      />

      <JsonLd
        type="breadcrumb"
        data={{
          items: generateBreadcrumbs(window.location.pathname, product.name)
        }}
      />

      <JsonLd
        type="faq"
        data={{
          faqs: generateProductFAQs(product)
        }}
      />

      <JsonLd
        type="organization"
        data={{
          address: "تهران، ایران",
          phone: "+98-21-12345678",
          postalCode: "1234567890",
          instagram: "https://instagram.com/glowroya",
          telegram: "https://t.me/glowroya",
          whatsapp: "https://wa.me/989123456789"
        }}
      />

      {/* Breadcrumb */}
      <div className="bg-white border-b border-gray-100">
        <div className="container-custom py-4">
          <nav className="flex items-center gap-2 text-sm">
            <Link 
              to="/" 
              className="text-text-secondary hover:text-primary-600 transition-colors"
            >
              خانه
            </Link>
            <ChevronLeft className="w-4 h-4 text-text-muted" />
            <Link 
              to="/products" 
              className="text-text-secondary hover:text-primary-600 transition-colors"
            >
              فروشگاه
            </Link>
            <ChevronLeft className="w-4 h-4 text-text-muted" />
            <Link 
              to={`/products?category=${product.category}`}
              className="text-text-secondary hover:text-primary-600 transition-colors"
            >
              {product.category}
            </Link>
            <ChevronLeft className="w-4 h-4 text-text-muted" />
            <span className="text-text-primary font-medium">
              {product.name}
            </span>
          </nav>
        </div>
      </div>

      <div className="container-custom py-8">
        {/* Back Button */}
        <motion.button
          onClick={() => navigate(-1)}
          className="flex items-center gap-2 text-text-secondary hover:text-primary-600 transition-colors mb-6"
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.3 }}
        >
          <ArrowRight className="w-4 h-4" />
          بازگشت
        </motion.button>

        {/* Product Main Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
          {/* Product Gallery */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
          >
            <ProductGallery 
              images={galleryImages} 
              productName={product.name} 
            />
          </motion.div>

          {/* Product Info */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <ProductInfo product={product} />
          </motion.div>
        </div>

        {/* Product Details Sections */}
        <div className="space-y-8">
          {/* Specifications */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            <ProductSpecifications product={product} />
          </motion.div>

          {/* Reviews */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.6 }}
          >
            <ReviewSystem product={product} />
          </motion.div>

          {/* Guarantee Information */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.7 }}
          >
            <GuaranteeInfo variant="product" showContactInfo={false} />
          </motion.div>

          {/* Related Products */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.8 }}
          >
            <RelatedProducts currentProduct={product} />
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default ProductDetail;
