import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Package, AlertTriangle } from 'lucide-react';
import { AdminFormLayout } from '../../../components/admin/layout/AdminLayout';
import AdminCard from '../../../components/admin/common/AdminCard';
import ProductForm from '../../../components/admin/products/ProductForm';
import { useAdminProducts } from '../../../hooks/useAdminProducts';
import { ProductFormData } from '../../../types/adminProduct';
import SEOHead from '../../../components/seo/SEOHead';
import toast from 'react-hot-toast';

const ProductEditPage: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const { getProduct, updateProduct, allProducts, loading } = useAdminProducts();
  const [submitting, setSubmitting] = useState(false);
  const [product, setProduct] = useState(getProduct(Number(id)));

  useEffect(() => {
    if (id) {
      const foundProduct = getProduct(Number(id));
      setProduct(foundProduct);
    }
  }, [id, getProduct]);

  if (!product) {
    return (
      <AdminFormLayout
        title="ویرایش محصول"
        subtitle="محصول یافت نشد"
      >
        <AdminCard>
          <div className="text-center py-8">
            <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">محصول یافت نشد</h3>
            <p className="text-gray-600 mb-4">
              محصول با شناسه {id} در سیستم موجود نیست.
            </p>
            <button
              onClick={() => navigate('/admin/products')}
              className="px-4 py-2 bg-admin-600 text-white rounded-lg hover:bg-admin-700 transition-colors"
            >
              بازگشت به لیست محصولات
            </button>
          </div>
        </AdminCard>
      </AdminFormLayout>
    );
  }

  const initialData: ProductFormData = {
    name: product.name || '',
    description: product.description || '',
    category: product.category || '',
    brand: product.brand || '',
    sku: product.sku || '',
    barcode: product.barcode || '',
    price: product.price || 0,
    discountedPrice: product.discountedPrice,
    costPrice: product.costPrice,
    compareAtPrice: product.compareAtPrice,
    taxable: product.taxable !== undefined ? product.taxable : true,
    taxClass: product.taxClass || '',
    stock: product.stock || 0,
    trackInventory: product.trackInventory !== undefined ? product.trackInventory : true,
    allowBackorder: product.allowBackorder !== undefined ? product.allowBackorder : false,
    lowStockThreshold: product.lowStockThreshold || 10,
    benefits: Array.isArray(product.benefits) ? [...product.benefits] : [],
    ingredients: Array.isArray(product.ingredients) ? [...product.ingredients] : [],
    howToUse: Array.isArray(product.howToUse) ? [...product.howToUse] : [],
    size: product.size || '',
    weight: product.weight || '',
    imageSrc: product.imageSrc || '',
    images: Array.isArray(product.images) ? [...product.images] : [],
    hasVariants: product.hasVariants !== undefined ? product.hasVariants : false,
    variants: Array.isArray(product.variants) ? [...product.variants] : [],
    seoTitle: product.seoTitle || product.name || '',
    seoDescription: product.seoDescription || '',
    seoKeywords: Array.isArray(product.seoKeywords) ? [...product.seoKeywords] : [],
    slug: product.slug || '',
    status: product.status || 'active',
    visibility: product.visibility || 'visible',
    featured: product.featured !== undefined ? product.featured : false,
    isNew: product.isNew !== undefined ? product.isNew : false,
    isBestSeller: product.isBestSeller !== undefined ? product.isBestSeller : false,
    requiresShipping: product.requiresShipping !== undefined ? product.requiresShipping : true,
    shippingWeight: product.shippingWeight,
    shippingDimensions: product.shippingDimensions,
    tags: Array.isArray(product.tags) ? [...product.tags] : [],
    vendor: product.vendor || '',
    productType: product.productType || '',
    collections: Array.isArray(product.collections) ? [...product.collections] : []
  };

  const handleSubmit = async (data: ProductFormData) => {
    try {
      setSubmitting(true);
      await updateProduct(product.id, data);
      toast.success('محصول با موفقیت بروزرسانی شد');
      navigate(`/admin/products/${product.id}`);
    } catch (error) {
      console.error('Error updating product:', error);
      toast.error('خطا در بروزرسانی محصول');
    } finally {
      setSubmitting(false);
    }
  };

  const handleCancel = () => {
    navigate(`/admin/products/${product.id}`);
  };

  // Exclude current product's SKU from validation
  const existingSkus = allProducts
    .filter(p => p.id !== product.id)
    .map(p => p.sku);

  return (
    <>
      <SEOHead
        title={`ویرایش ${product.name} | پنل مدیریت گلو رویا`}
        description={`ویرایش محصول ${product.name} در پنل مدیریت`}
        robots="noindex, nofollow"
      />

      <AdminFormLayout
        title={`ویرایش ${product.name}`}
        subtitle={`SKU: ${product.sku}`}
      >
        <ProductForm
          initialData={initialData}
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          loading={submitting}
          existingSkus={existingSkus}
        />
      </AdminFormLayout>
    </>
  );
};

export default ProductEditPage;
