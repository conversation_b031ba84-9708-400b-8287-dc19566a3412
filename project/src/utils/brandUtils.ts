import { AdminBrandService } from '../services/adminBrandService';

// Brand data structure
export interface Brand {
  id: string;
  name: string;
  nameEn?: string;
  slug: string;
  description?: string;
  logo?: string;
  website?: string;
  country?: string;
  isActive: boolean;
  productsCount: number;
  createdAt: string;
  updatedAt: string;
}

// Cache for brand data
let brandsCache: Brand[] | null = null;
let cacheTimestamp: number = 0;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

// Backend API base URL
const API_BASE_URL = 'http://localhost:3001';

// Helper function to get full logo URL
const getFullLogoUrl = (logoPath?: string): string | undefined => {
  if (!logoPath) return undefined;
  if (logoPath.startsWith('http')) return logoPath;
  return `${API_BASE_URL}${logoPath}`;
};

// Load brands from backend API
const loadBrandsFromAPI = async (): Promise<Brand[]> => {
  try {
    const response = await AdminBrandService.getBrands({
      includeInactive: false,
      includeProductCount: true,
      limit: 100
    });

    return response.brands.map(brand => ({
      ...brand,
      logo: getFullLogoUrl(brand.logo)
    }));
  } catch (error) {
    console.warn('Failed to load brands from API, using fallback data:', error);

    // Fallback brand data with backend-compatible structure
    return [
      {
        id: '1',
        name: 'گلو رویا',
        nameEn: 'GlowRoya',
        slug: 'glowroya',
        description: 'برند ایرانی مراقبت از پوست با فرمولاسیون پیشرفته',
        logo: getFullLogoUrl('/uploads/assets/app-logo.png'),
        website: 'https://glowroya.ir',
        country: 'ایران',
        isActive: true,
        productsCount: 45,
        createdAt: '2024-01-15',
        updatedAt: '2024-03-10'
      },
      {
        id: '2',
        name: 'لورآل',
        nameEn: 'L\'Oreal',
        slug: 'loreal',
        description: 'برند معروف فرانسوی آرایشی و بهداشتی',
        logo: getFullLogoUrl('/uploads/brands/loreal-logo.jpg'),
        website: 'https://loreal.com',
        country: 'فرانسه',
        isActive: true,
        productsCount: 23,
        createdAt: '2024-01-20',
        updatedAt: '2024-03-05'
      },
      {
        id: '3',
        name: 'نیویا',
        nameEn: 'Nivea',
        slug: 'nivea',
        description: 'برند معروف آلمانی مراقبت از پوست',
        logo: getFullLogoUrl('/uploads/brands/nivea-logo.png'),
        website: 'https://nivea.com',
        country: 'آلمان',
        isActive: true,
        productsCount: 18,
        createdAt: '2024-02-01',
        updatedAt: '2024-03-08'
      },
      {
        id: '4',
        name: 'سراوی',
        nameEn: 'CeraVe',
        slug: 'cerave',
        description: 'برند معروف آمریکایی مراقبت از پوست با سرامید',
        logo: getFullLogoUrl('/uploads/brands/cerave-logo.png'),
        website: 'https://cerave.com',
        country: 'آمریکا',
        isActive: true,
        productsCount: 8,
        createdAt: '2024-02-15',
        updatedAt: '2024-03-12'
      },
      {
        id: '5',
        name: 'گارنیه',
        nameEn: 'Garnier',
        slug: 'garnier',
        description: 'برند فرانسوی محصولات طبیعی مراقبت از پوست',
        logo: getFullLogoUrl('/uploads/brands/garnier-logo.png'),
        website: 'https://garnier.com',
        country: 'فرانسه',
        isActive: true,
        productsCount: 12,
        createdAt: '2024-02-10',
        updatedAt: '2024-03-01'
      }
    ];
  }
};

// Get brands with caching
const getBrands = async (): Promise<Brand[]> => {
  const now = Date.now();

  // Return cached data if still valid
  if (brandsCache && (now - cacheTimestamp) < CACHE_DURATION) {
    return brandsCache;
  }

  // Load fresh data
  brandsCache = await loadBrandsFromAPI();
  cacheTimestamp = now;

  return brandsCache;
};

/**
 * Get brand by name (synchronous with cache)
 */
export const getBrandByName = (brandName: string): Brand | undefined => {
  if (!brandsCache) {
    // If no cache, try to load synchronously from fallback
    return undefined;
  }

  return brandsCache.find(brand =>
    brand.name === brandName ||
    (brand.nameEn && brand.nameEn.toLowerCase() === brandName.toLowerCase()) ||
    brand.slug === brandName.toLowerCase()
  );
};

/**
 * Get brand by name (async version)
 */
export const getBrandByNameAsync = async (brandName: string): Promise<Brand | undefined> => {
  const brands = await getBrands();
  return brands.find(brand =>
    brand.name === brandName ||
    (brand.nameEn && brand.nameEn.toLowerCase() === brandName.toLowerCase()) ||
    brand.slug === brandName.toLowerCase()
  );
};

/**
 * Get brand logo by name
 */
export const getBrandLogo = (brandName: string): string | undefined => {
  const brand = getBrandByName(brandName);
  return brand?.logo;
};

/**
 * Get all active brands (async)
 */
export const getActiveBrands = async (): Promise<Brand[]> => {
  const brands = await getBrands();
  return brands.filter(brand => brand.isActive);
};

/**
 * Get brand options for select components (async)
 */
export const getBrandOptions = async () => {
  const activeBrands = await getActiveBrands();
  return activeBrands.map(brand => ({
    value: brand.name,
    label: brand.name,
    logo: brand.logo
  }));
};

/**
 * Get brand info for display (with fallback for immediate use)
 */
export const getBrandInfo = (brandName: string) => {
  const brand = getBrandByName(brandName);
  if (!brand) {
    // Return fallback info for unknown brands
    return {
      name: brandName,
      nameEn: brandName,
      logo: undefined,
      website: undefined,
      country: undefined,
      isActive: true
    };
  }

  return {
    name: brand.name,
    nameEn: brand.nameEn,
    logo: brand.logo,
    website: brand.website,
    country: brand.country,
    isActive: brand.isActive
  };
};

/**
 * Get brand info for display (async version)
 */
export const getBrandInfoAsync = async (brandName: string) => {
  const brand = await getBrandByNameAsync(brandName);
  if (!brand) {
    return {
      name: brandName,
      nameEn: brandName,
      logo: undefined,
      website: undefined,
      country: undefined,
      isActive: true
    };
  }

  return {
    name: brand.name,
    nameEn: brand.nameEn,
    logo: brand.logo,
    website: brand.website,
    country: brand.country,
    isActive: brand.isActive
  };
};

/**
 * Check if brand exists
 */
export const brandExists = (brandName: string): boolean => {
  return getBrandByName(brandName) !== undefined;
};

/**
 * Get brand statistics (async)
 */
export const getBrandStats = async () => {
  const brands = await getBrands();
  return {
    total: brands.length,
    active: brands.filter(b => b.isActive).length,
    inactive: brands.filter(b => !b.isActive).length,
    totalProducts: brands.reduce((sum, b) => sum + b.productsCount, 0)
  };
};

/**
 * Initialize brands cache (call this early in app lifecycle)
 */
export const initializeBrands = async (): Promise<void> => {
  try {
    await getBrands();
    console.log('✅ Brands cache initialized successfully');
  } catch (error) {
    console.warn('⚠️ Failed to initialize brands cache:', error);
  }
};
