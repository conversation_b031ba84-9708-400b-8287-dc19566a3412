import { Product, ProductCategory, ProductImage, ProductInventory, Prisma } from '@prisma/client';
import { prisma } from '../config/database';
import { AppError } from '../middleware/errorHandler';
import { logger } from '../config/logger';
import UploadService from './uploadService';

// Types for product operations
export interface CreateProductData {
  name: string;
  nameEn?: string;
  slug: string;
  description?: string;
  shortDescription?: string;
  sku: string;
  barcode?: string;
  brandId?: string;
  price: number;
  comparePrice?: number;
  costPrice?: number;
  weight?: number;
  dimensions?: {
    length: number;
    width: number;
    height: number;
  };
  isActive?: boolean;
  isFeatured?: boolean;
  isDigital?: boolean;
  requiresShipping?: boolean;
  trackQuantity?: boolean;
  allowBackorder?: boolean;
  metaTitle?: string;
  metaDescription?: string;
  tags?: string[];
  categoryIds?: string[];
}

export interface UpdateProductData extends Partial<CreateProductData> {}

export interface ProductFilters {
  search?: string;
  categoryId?: string;
  brandId?: string;
  isActive?: boolean;
  isFeatured?: boolean;
  isDigital?: boolean;
  minPrice?: number;
  maxPrice?: number;
  tags?: string[];
  inStock?: boolean;
}

export interface ProductQueryOptions {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  include?: {
    brand?: boolean;
    categories?: boolean;
    images?: boolean;
    variants?: boolean;
    inventory?: boolean;
    reviews?: boolean;
  };
}

// Product with relations type
export type ProductWithRelations = Product & {
  brand?: any;
  categories?: (ProductCategory & { category: any })[];
  images?: ProductImage[];
  variants?: any[];
  inventory?: ProductInventory;
  reviews?: any[];
  _count?: {
    reviews: number;
    variants: number;
  };
};

export class ProductService {
  // Create a new product
  static async createProduct(data: CreateProductData): Promise<ProductWithRelations> {
    try {
      // Check if slug already exists
      const existingProduct = await prisma.product.findUnique({
        where: { slug: data.slug },
      });

      if (existingProduct) {
        throw new AppError('نامک محصول قبلاً استفاده شده است', 409, 'SLUG_EXISTS');
      }

      // Check if SKU already exists
      const existingSku = await prisma.product.findUnique({
        where: { sku: data.sku },
      });

      if (existingSku) {
        throw new AppError('کد محصول قبلاً استفاده شده است', 409, 'SKU_EXISTS');
      }

      // Validate brand if provided
      if (data.brandId) {
        const brand = await prisma.brand.findUnique({
          where: { id: data.brandId },
        });

        if (!brand) {
          throw new AppError('برند مورد نظر یافت نشد', 404, 'BRAND_NOT_FOUND');
        }
      }

      // Validate categories if provided
      if (data.categoryIds && data.categoryIds.length > 0) {
        const categories = await prisma.category.findMany({
          where: { id: { in: data.categoryIds } },
        });

        if (categories.length !== data.categoryIds.length) {
          throw new AppError('یکی یا چند دسته‌بندی یافت نشد', 404, 'CATEGORIES_NOT_FOUND');
        }
      }

      // Create product with transaction
      const result = await prisma.$transaction(async (tx) => {
        // Create the product
        const product = await tx.product.create({
          data: {
            name: data.name,
            nameEn: data.nameEn,
            slug: data.slug,
            description: data.description,
            shortDescription: data.shortDescription,
            sku: data.sku,
            barcode: data.barcode,
            brandId: data.brandId,
            price: data.price,
            comparePrice: data.comparePrice,
            costPrice: data.costPrice,
            weight: data.weight,
            dimensions: data.dimensions,
            isActive: data.isActive ?? true,
            isFeatured: data.isFeatured ?? false,
            isDigital: data.isDigital ?? false,
            requiresShipping: data.requiresShipping ?? true,
            trackQuantity: data.trackQuantity ?? true,
            allowBackorder: data.allowBackorder ?? false,
            metaTitle: data.metaTitle,
            metaDescription: data.metaDescription,
            tags: data.tags || [],
          },
        });

        // Create category associations
        if (data.categoryIds && data.categoryIds.length > 0) {
          await tx.productCategory.createMany({
            data: data.categoryIds.map(categoryId => ({
              productId: product.id,
              categoryId,
            })),
          });
        }

        // Create initial inventory record if tracking quantity
        if (data.trackQuantity !== false) {
          await tx.productInventory.create({
            data: {
              productId: product.id,
              quantity: 0,
              lowStockThreshold: 10,
            },
          });
        }

        return product;
      });

      logger.info(`Product created successfully: ${result.name} (${result.sku})`);

      // Return product with relations
      return await this.getProductById(result.id, {
        include: {
          brand: true,
          categories: true,
          images: true,
          inventory: true,
        },
      });
    } catch (error) {
      logger.error('Product creation failed:', error);
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError('خطا در ایجاد محصول', 500, 'PRODUCT_CREATION_FAILED');
    }
  }

  // Get product by ID
  static async getProductById(
    id: string,
    options: ProductQueryOptions = {}
  ): Promise<ProductWithRelations> {
    try {
      const include: any = {};

      if (options.include?.brand) include.brand = true;
      if (options.include?.categories) {
        include.categories = {
          include: { category: true },
        };
      }
      if (options.include?.images) {
        include.images = {
          orderBy: { sortOrder: 'asc' },
        };
      }
      if (options.include?.variants) {
        include.variants = {
          where: { isActive: true },
          orderBy: { sortOrder: 'asc' },
        };
      }
      if (options.include?.inventory) include.inventory = true;
      if (options.include?.reviews) {
        include.reviews = {
          where: { isApproved: true },
          include: { user: { select: { firstName: true, lastName: true } } },
          orderBy: { createdAt: 'desc' },
          take: 10,
        };
        include._count = { select: { reviews: true } };
      }

      const product = await prisma.product.findUnique({
        where: { id },
        include,
      });

      if (!product) {
        throw new AppError('محصول یافت نشد', 404, 'PRODUCT_NOT_FOUND');
      }

      return product as ProductWithRelations;
    } catch (error) {
      logger.error('Get product by ID failed:', error);
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError('خطا در دریافت محصول', 500, 'GET_PRODUCT_FAILED');
    }
  }

  // Get product by slug
  static async getProductBySlug(
    slug: string,
    options: ProductQueryOptions = {}
  ): Promise<ProductWithRelations> {
    try {
      const include: any = {};

      if (options.include?.brand) include.brand = true;
      if (options.include?.categories) {
        include.categories = {
          include: { category: true },
        };
      }
      if (options.include?.images) {
        include.images = {
          orderBy: { sortOrder: 'asc' },
        };
      }
      if (options.include?.variants) {
        include.variants = {
          where: { isActive: true },
          orderBy: { sortOrder: 'asc' },
        };
      }
      if (options.include?.inventory) include.inventory = true;
      if (options.include?.reviews) {
        include.reviews = {
          where: { isApproved: true },
          include: { user: { select: { firstName: true, lastName: true } } },
          orderBy: { createdAt: 'desc' },
          take: 10,
        };
        include._count = { select: { reviews: true } };
      }

      const product = await prisma.product.findUnique({
        where: { slug },
        include,
      });

      if (!product) {
        throw new AppError('محصول یافت نشد', 404, 'PRODUCT_NOT_FOUND');
      }

      return product as ProductWithRelations;
    } catch (error) {
      logger.error('Get product by slug failed:', error);
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError('خطا در دریافت محصول', 500, 'GET_PRODUCT_FAILED');
    }
  }

  // Get all products with filtering and pagination
  static async getProducts(
    filters: ProductFilters = {},
    options: ProductQueryOptions = {}
  ): Promise<{
    products: ProductWithRelations[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  }> {
    try {
      const page = options.page || 1;
      const limit = Math.min(options.limit || 20, 100); // Max 100 items per page
      const skip = (page - 1) * limit;

      // Build where clause
      const where: Prisma.ProductWhereInput = {};

      if (filters.search) {
        where.OR = [
          { name: { contains: filters.search, mode: 'insensitive' } },
          { nameEn: { contains: filters.search, mode: 'insensitive' } },
          { description: { contains: filters.search, mode: 'insensitive' } },
          { sku: { contains: filters.search, mode: 'insensitive' } },
          { tags: { has: filters.search } },
        ];
      }

      if (filters.categoryId) {
        where.categories = {
          some: { categoryId: filters.categoryId },
        };
      }

      if (filters.brandId) {
        where.brandId = filters.brandId;
      }

      if (filters.isActive !== undefined) {
        where.isActive = filters.isActive;
      }

      if (filters.isFeatured !== undefined) {
        where.isFeatured = filters.isFeatured;
      }

      if (filters.isDigital !== undefined) {
        where.isDigital = filters.isDigital;
      }

      if (filters.minPrice !== undefined || filters.maxPrice !== undefined) {
        where.price = {};
        if (filters.minPrice !== undefined) {
          where.price.gte = filters.minPrice;
        }
        if (filters.maxPrice !== undefined) {
          where.price.lte = filters.maxPrice;
        }
      }

      if (filters.tags && filters.tags.length > 0) {
        where.tags = {
          hasSome: filters.tags,
        };
      }

      if (filters.inStock) {
        where.inventory = {
          quantity: { gt: 0 },
        };
      }

      // Build include clause
      const include: any = {};
      if (options.include?.brand) include.brand = true;
      if (options.include?.categories) {
        include.categories = {
          include: { category: true },
        };
      }
      if (options.include?.images) {
        include.images = {
          orderBy: { sortOrder: 'asc' },
        };
      }
      if (options.include?.variants) {
        include.variants = {
          where: { isActive: true },
          orderBy: { sortOrder: 'asc' },
        };
      }
      if (options.include?.inventory) include.inventory = true;
      if (options.include?.reviews) {
        include._count = { select: { reviews: true } };
      }

      // Build order by clause
      const orderBy: Prisma.ProductOrderByWithRelationInput = {};
      const sortBy = options.sortBy || 'createdAt';
      const sortOrder = options.sortOrder || 'desc';

      switch (sortBy) {
        case 'name':
          orderBy.name = sortOrder;
          break;
        case 'price':
          orderBy.price = sortOrder;
          break;
        case 'createdAt':
          orderBy.createdAt = sortOrder;
          break;
        case 'updatedAt':
          orderBy.updatedAt = sortOrder;
          break;
        default:
          orderBy.createdAt = 'desc';
      }

      // Get total count
      const total = await prisma.product.count({ where });

      // Get products
      const products = await prisma.product.findMany({
        where,
        include,
        orderBy,
        skip,
        take: limit,
      });

      const totalPages = Math.ceil(total / limit);

      return {
        products: products as ProductWithRelations[],
        pagination: {
          page,
          limit,
          total,
          totalPages,
        },
      };
    } catch (error) {
      logger.error('Get products failed:', error);
      throw new AppError('خطا در دریافت محصولات', 500, 'GET_PRODUCTS_FAILED');
    }
  }

  // Update product
  static async updateProduct(
    id: string,
    data: UpdateProductData
  ): Promise<ProductWithRelations> {
    try {
      // Check if product exists
      const existingProduct = await prisma.product.findUnique({
        where: { id },
      });

      if (!existingProduct) {
        throw new AppError('محصول یافت نشد', 404, 'PRODUCT_NOT_FOUND');
      }

      // Check if slug is being updated and already exists
      if (data.slug && data.slug !== existingProduct.slug) {
        const slugExists = await prisma.product.findUnique({
          where: { slug: data.slug },
        });

        if (slugExists) {
          throw new AppError('نامک محصول قبلاً استفاده شده است', 409, 'SLUG_EXISTS');
        }
      }

      // Check if SKU is being updated and already exists
      if (data.sku && data.sku !== existingProduct.sku) {
        const skuExists = await prisma.product.findUnique({
          where: { sku: data.sku },
        });

        if (skuExists) {
          throw new AppError('کد محصول قبلاً استفاده شده است', 409, 'SKU_EXISTS');
        }
      }

      // Validate brand if provided
      if (data.brandId) {
        const brand = await prisma.brand.findUnique({
          where: { id: data.brandId },
        });

        if (!brand) {
          throw new AppError('برند مورد نظر یافت نشد', 404, 'BRAND_NOT_FOUND');
        }
      }

      // Update product with transaction
      const result = await prisma.$transaction(async (tx) => {
        // Update the product
        const updatedProduct = await tx.product.update({
          where: { id },
          data: {
            name: data.name,
            nameEn: data.nameEn,
            slug: data.slug,
            description: data.description,
            shortDescription: data.shortDescription,
            sku: data.sku,
            barcode: data.barcode,
            brandId: data.brandId,
            price: data.price,
            comparePrice: data.comparePrice,
            costPrice: data.costPrice,
            weight: data.weight,
            dimensions: data.dimensions,
            isActive: data.isActive,
            isFeatured: data.isFeatured,
            isDigital: data.isDigital,
            requiresShipping: data.requiresShipping,
            trackQuantity: data.trackQuantity,
            allowBackorder: data.allowBackorder,
            metaTitle: data.metaTitle,
            metaDescription: data.metaDescription,
            tags: data.tags,
          },
        });

        // Update category associations if provided
        if (data.categoryIds !== undefined) {
          // Remove existing associations
          await tx.productCategory.deleteMany({
            where: { productId: id },
          });

          // Create new associations
          if (data.categoryIds.length > 0) {
            // Validate categories
            const categories = await tx.category.findMany({
              where: { id: { in: data.categoryIds } },
            });

            if (categories.length !== data.categoryIds.length) {
              throw new AppError('یکی یا چند دسته‌بندی یافت نشد', 404, 'CATEGORIES_NOT_FOUND');
            }

            await tx.productCategory.createMany({
              data: data.categoryIds.map(categoryId => ({
                productId: id,
                categoryId,
              })),
            });
          }
        }

        return updatedProduct;
      });

      logger.info(`Product updated successfully: ${result.name} (${result.sku})`);

      // Return product with relations
      return await this.getProductById(result.id, {
        include: {
          brand: true,
          categories: true,
          images: true,
          inventory: true,
        },
      });
    } catch (error) {
      logger.error('Product update failed:', error);
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError('خطا در به‌روزرسانی محصول', 500, 'PRODUCT_UPDATE_FAILED');
    }
  }

  // Delete product
  static async deleteProduct(id: string): Promise<void> {
    try {
      // Check if product exists
      const existingProduct = await prisma.product.findUnique({
        where: { id },
        include: {
          images: true,
          orderItems: true,
        },
      });

      if (!existingProduct) {
        throw new AppError('محصول یافت نشد', 404, 'PRODUCT_NOT_FOUND');
      }

      // Check if product has orders
      if (existingProduct.orderItems.length > 0) {
        throw new AppError(
          'امکان حذف محصول وجود ندارد زیرا در سفارشات استفاده شده است',
          400,
          'PRODUCT_HAS_ORDERS'
        );
      }

      // Delete product with transaction
      await prisma.$transaction(async (tx) => {
        // Delete product images from filesystem
        if (existingProduct.images.length > 0) {
          // Extract filenames from URLs
          const filenames = existingProduct.images.map(img => {
            const urlParts = img.url.split('/');
            return urlParts[urlParts.length - 1];
          });
          await UploadService.deleteFiles(filenames, 'products');
        }

        // Delete the product (cascade will handle related records)
        await tx.product.delete({
          where: { id },
        });
      });

      logger.info(`Product deleted successfully: ${existingProduct.name} (${existingProduct.sku})`);
    } catch (error) {
      logger.error('Product deletion failed:', error);
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError('خطا در حذف محصول', 500, 'PRODUCT_DELETION_FAILED');
    }
  }

  // Add images to product
  static async addProductImages(
    productId: string,
    images: Array<{
      url: string;
      alt?: string;
      isPrimary?: boolean;
      sortOrder?: number;
    }>
  ): Promise<ProductImage[]> {
    try {
      // Check if product exists
      const product = await prisma.product.findUnique({
        where: { id: productId },
      });

      if (!product) {
        throw new AppError('محصول یافت نشد', 404, 'PRODUCT_NOT_FOUND');
      }

      // Create images
      const createdImages = await prisma.$transaction(async (tx) => {
        const imagePromises = images.map((image, index) =>
          tx.productImage.create({
            data: {
              productId,
              url: image.url,
              alt: image.alt,
              isPrimary: image.isPrimary || false,
              sortOrder: image.sortOrder || index,
            },
          })
        );

        return await Promise.all(imagePromises);
      });

      logger.info(`Added ${createdImages.length} images to product: ${productId}`);
      return createdImages;
    } catch (error) {
      logger.error('Add product images failed:', error);
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError('خطا در افزودن تصاویر محصول', 500, 'ADD_IMAGES_FAILED');
    }
  }

  // Remove product image
  static async removeProductImage(productId: string, imageId: string): Promise<void> {
    try {
      // Find the image
      const image = await prisma.productImage.findFirst({
        where: {
          id: imageId,
          productId,
        },
      });

      if (!image) {
        throw new AppError('تصویر یافت نشد', 404, 'IMAGE_NOT_FOUND');
      }

      // Delete image from database and filesystem
      await prisma.$transaction(async (tx) => {
        await tx.productImage.delete({
          where: { id: imageId },
        });

        // Delete file from filesystem - extract filename from URL
        const urlParts = image.url.split('/');
        const filename = urlParts[urlParts.length - 1];
        await UploadService.deleteFile(filename, 'products');
      });

      logger.info(`Product image removed: ${imageId} from product: ${productId}`);
    } catch (error) {
      logger.error('Remove product image failed:', error);
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError('خطا در حذف تصویر محصول', 500, 'REMOVE_IMAGE_FAILED');
    }
  }

  // Update product inventory
  static async updateInventory(
    productId: string,
    data: {
      quantity?: number;
      lowStockThreshold?: number;
      location?: string;
    }
  ): Promise<ProductInventory> {
    try {
      // Check if product exists
      const product = await prisma.product.findUnique({
        where: { id: productId },
      });

      if (!product) {
        throw new AppError('محصول یافت نشد', 404, 'PRODUCT_NOT_FOUND');
      }

      // Update or create inventory
      const inventory = await prisma.productInventory.upsert({
        where: { productId },
        update: {
          quantity: data.quantity,
          lowStockThreshold: data.lowStockThreshold,
          location: data.location,
        },
        create: {
          productId,
          quantity: data.quantity || 0,
          lowStockThreshold: data.lowStockThreshold || 10,
          location: data.location,
        },
      });

      logger.info(`Product inventory updated: ${productId}, quantity: ${inventory.quantity}`);
      return inventory;
    } catch (error) {
      logger.error('Update product inventory failed:', error);
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError('خطا در به‌روزرسانی موجودی محصول', 500, 'UPDATE_INVENTORY_FAILED');
    }
  }

  // Get low stock products
  static async getLowStockProducts(): Promise<ProductWithRelations[]> {
    try {
      const products = await prisma.product.findMany({
        where: {
          isActive: true,
          trackQuantity: true,
          inventory: {
            quantity: {
              lte: prisma.productInventory.fields.lowStockThreshold,
            },
          },
        },
        include: {
          brand: true,
          categories: {
            include: { category: true },
          },
          inventory: true,
        },
        orderBy: {
          inventory: {
            quantity: 'asc',
          },
        },
      });

      return products as ProductWithRelations[];
    } catch (error) {
      logger.error('Get low stock products failed:', error);
      throw new AppError('خطا در دریافت محصولات کم‌موجود', 500, 'GET_LOW_STOCK_FAILED');
    }
  }

  // Bulk update products
  static async bulkUpdateProducts(
    productIds: string[],
    data: {
      isActive?: boolean;
      isFeatured?: boolean;
      categoryIds?: string[];
      price?: number;
      comparePrice?: number;
    }
  ): Promise<number> {
    try {
      const updateData: any = {};

      if (data.isActive !== undefined) updateData.isActive = data.isActive;
      if (data.isFeatured !== undefined) updateData.isFeatured = data.isFeatured;
      if (data.price !== undefined) updateData.price = data.price;
      if (data.comparePrice !== undefined) updateData.comparePrice = data.comparePrice;

      const result = await prisma.$transaction(async (tx) => {
        // Update products
        const updateResult = await tx.product.updateMany({
          where: { id: { in: productIds } },
          data: updateData,
        });

        // Update categories if provided
        if (data.categoryIds !== undefined) {
          // Remove existing category associations
          await tx.productCategory.deleteMany({
            where: { productId: { in: productIds } },
          });

          // Add new category associations
          if (data.categoryIds.length > 0) {
            const categoryAssociations = productIds.flatMap(productId =>
              data.categoryIds!.map(categoryId => ({
                productId,
                categoryId,
              }))
            );

            await tx.productCategory.createMany({
              data: categoryAssociations,
            });
          }
        }

        return updateResult.count;
      });

      logger.info(`Bulk updated ${result} products`);
      return result;
    } catch (error) {
      logger.error('Bulk update products failed:', error);
      throw new AppError('خطا در به‌روزرسانی گروهی محصولات', 500, 'BULK_UPDATE_FAILED');
    }
  }
}
