<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test User Management Access</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .step {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .step h3 {
            margin-top: 0;
            color: #333;
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            text-decoration: none;
            display: inline-block;
        }
        .button:hover {
            background: #0056b3;
        }
        .button.success {
            background: #28a745;
        }
        .button.danger {
            background: #dc3545;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test User Management Access</h1>
        <p>This tool will help you test the user management permission system step by step.</p>

        <div class="step">
            <h3>Step 1: Clear Current Session</h3>
            <p>First, clear any existing admin session to start fresh.</p>
            <button class="button danger" onclick="clearSession()">Clear Session</button>
            <div id="clearStatus"></div>
        </div>

        <div class="step">
            <h3>Step 2: Login as Super Admin</h3>
            <p>Login with the super admin account that has user management permissions.</p>
            <button class="button success" onclick="loginSuperAdmin()">Login as Super Admin</button>
            <div id="loginStatus"></div>
        </div>

        <div class="step">
            <h3>Step 3: Verify Authentication</h3>
            <p>Check if the authentication was successful and user data is stored correctly.</p>
            <button class="button" onclick="verifyAuth()">Verify Authentication</button>
            <div id="authStatus"></div>
        </div>

        <div class="step">
            <h3>Step 4: Test User Management Access</h3>
            <p>Try to access the user management pages.</p>
            <a href="http://localhost:5173/admin/users" target="_blank" class="button">Open /admin/users</a>
            <a href="http://localhost:5173/admin/users/admins" target="_blank" class="button">Open /admin/users/admins</a>
            <a href="http://localhost:5173/admin/users/roles" target="_blank" class="button">Open /admin/users/roles</a>
            <div id="accessStatus"></div>
        </div>

        <div class="step">
            <h3>Step 5: Test with Different Roles</h3>
            <p>Test access with different admin roles to verify permission restrictions.</p>
            <button class="button" onclick="loginAdmin()">Login as Admin (No Access)</button>
            <button class="button" onclick="loginModerator()">Login as Moderator (No Access)</button>
            <div id="roleTestStatus"></div>
        </div>

        <div class="step">
            <h3>Step 6: Debug Information</h3>
            <p>View current authentication state and debug information.</p>
            <button class="button" onclick="showDebugInfo()">Show Debug Info</button>
            <div id="debugInfo"></div>
        </div>
    </div>

    <script>
        const ADMIN_STORAGE_KEYS = {
            TOKEN: 'admin_token',
            REFRESH_TOKEN: 'admin_refresh_token',
            USER: 'admin_user',
            SESSION_EXPIRY: 'admin_session_expiry',
            REMEMBER_ME: 'admin_remember_me'
        };

        function clearSession() {
            Object.values(ADMIN_STORAGE_KEYS).forEach(key => {
                localStorage.removeItem(key);
            });
            document.getElementById('clearStatus').innerHTML = 
                '<div class="status success">✅ Session cleared successfully</div>';
        }

        function createMockToken(user, rememberMe = false) {
            const now = Math.floor(Date.now() / 1000);
            const expiry = now + (rememberMe ? 30 * 24 * 60 * 60 : 24 * 60 * 60);
            
            const tokenPayload = {
                sub: user.id,
                email: user.email,
                role: user.role,
                type: 'admin',
                iat: now,
                exp: expiry
            };

            return btoa(JSON.stringify({ alg: 'HS256', typ: 'JWT' })) + '.' +
                   btoa(JSON.stringify(tokenPayload)) + '.' +
                   btoa('mock_signature');
        }

        function loginSuperAdmin() {
            const user = {
                id: 'admin-1',
                email: '<EMAIL>',
                firstName: 'مدیر',
                lastName: 'سیستم',
                role: 'super_admin',
                isActive: true,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
                twoFactorEnabled: false,
                department: 'مدیریت',
                permissions: [
                    { resource: 'users', actions: ['create', 'read', 'update', 'delete'] }
                ]
            };

            const token = createMockToken(user, true);
            const refreshToken = 'mock_refresh_token_' + Date.now();
            const sessionExpiry = Date.now() + (24 * 60 * 60 * 1000);

            localStorage.setItem(ADMIN_STORAGE_KEYS.TOKEN, token);
            localStorage.setItem(ADMIN_STORAGE_KEYS.REFRESH_TOKEN, refreshToken);
            localStorage.setItem(ADMIN_STORAGE_KEYS.USER, JSON.stringify(user));
            localStorage.setItem(ADMIN_STORAGE_KEYS.SESSION_EXPIRY, sessionExpiry.toString());
            localStorage.setItem(ADMIN_STORAGE_KEYS.REMEMBER_ME, 'true');

            document.getElementById('loginStatus').innerHTML = 
                '<div class="status success">✅ Logged in as Super Admin (<EMAIL>)</div>';
        }

        function loginAdmin() {
            const user = {
                id: 'admin-2',
                email: '<EMAIL>',
                firstName: 'مدیر',
                lastName: 'فروش',
                role: 'admin',
                isActive: true,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
                twoFactorEnabled: false,
                department: 'فروش',
                permissions: []
            };

            const token = createMockToken(user, true);
            const refreshToken = 'mock_refresh_token_' + Date.now();
            const sessionExpiry = Date.now() + (24 * 60 * 60 * 1000);

            localStorage.setItem(ADMIN_STORAGE_KEYS.TOKEN, token);
            localStorage.setItem(ADMIN_STORAGE_KEYS.REFRESH_TOKEN, refreshToken);
            localStorage.setItem(ADMIN_STORAGE_KEYS.USER, JSON.stringify(user));
            localStorage.setItem(ADMIN_STORAGE_KEYS.SESSION_EXPIRY, sessionExpiry.toString());
            localStorage.setItem(ADMIN_STORAGE_KEYS.REMEMBER_ME, 'true');

            document.getElementById('roleTestStatus').innerHTML = 
                '<div class="status warning">⚠️ Logged in as Admin - Should NOT have user management access</div>';
        }

        function loginModerator() {
            const user = {
                id: 'admin-3',
                email: '<EMAIL>',
                firstName: 'ناظر',
                lastName: 'محتوا',
                role: 'moderator',
                isActive: true,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
                twoFactorEnabled: false,
                department: 'محتوا',
                permissions: []
            };

            const token = createMockToken(user, true);
            const refreshToken = 'mock_refresh_token_' + Date.now();
            const sessionExpiry = Date.now() + (24 * 60 * 60 * 1000);

            localStorage.setItem(ADMIN_STORAGE_KEYS.TOKEN, token);
            localStorage.setItem(ADMIN_STORAGE_KEYS.REFRESH_TOKEN, refreshToken);
            localStorage.setItem(ADMIN_STORAGE_KEYS.USER, JSON.stringify(user));
            localStorage.setItem(ADMIN_STORAGE_KEYS.SESSION_EXPIRY, sessionExpiry.toString());
            localStorage.setItem(ADMIN_STORAGE_KEYS.REMEMBER_ME, 'true');

            document.getElementById('roleTestStatus').innerHTML = 
                '<div class="status warning">⚠️ Logged in as Moderator - Should NOT have user management access</div>';
        }

        function verifyAuth() {
            const token = localStorage.getItem(ADMIN_STORAGE_KEYS.TOKEN);
            const user = localStorage.getItem(ADMIN_STORAGE_KEYS.USER);
            const sessionExpiry = localStorage.getItem(ADMIN_STORAGE_KEYS.SESSION_EXPIRY);

            let status = '';
            let isValid = true;

            if (!token) {
                status += '<div class="status error">❌ No token found</div>';
                isValid = false;
            } else {
                status += '<div class="status success">✅ Token exists</div>';
            }

            if (!user) {
                status += '<div class="status error">❌ No user data found</div>';
                isValid = false;
            } else {
                try {
                    const userData = JSON.parse(user);
                    status += `<div class="status success">✅ User: ${userData.firstName} ${userData.lastName} (${userData.role})</div>`;
                    
                    if (userData.role === 'super_admin') {
                        status += '<div class="status success">✅ User has super_admin role - Should have user management access</div>';
                    } else {
                        status += '<div class="status warning">⚠️ User does not have super_admin role - Should NOT have user management access</div>';
                    }
                } catch (e) {
                    status += '<div class="status error">❌ Invalid user data format</div>';
                    isValid = false;
                }
            }

            if (sessionExpiry) {
                const expiry = parseInt(sessionExpiry);
                const now = Date.now();
                if (expiry > now) {
                    status += `<div class="status success">✅ Session valid until: ${new Date(expiry).toLocaleString()}</div>`;
                } else {
                    status += `<div class="status error">❌ Session expired at: ${new Date(expiry).toLocaleString()}</div>`;
                    isValid = false;
                }
            }

            document.getElementById('authStatus').innerHTML = status;
        }

        function showDebugInfo() {
            const storage = {};
            Object.values(ADMIN_STORAGE_KEYS).forEach(key => {
                const value = localStorage.getItem(key);
                storage[key] = value ? (key === ADMIN_STORAGE_KEYS.USER ? JSON.parse(value) : value) : null;
            });

            document.getElementById('debugInfo').innerHTML = 
                '<pre style="background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto;">' +
                JSON.stringify(storage, null, 2) +
                '</pre>';
        }
    </script>
</body>
</html>
