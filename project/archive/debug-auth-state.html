<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Admin Auth State</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h3 {
            margin-top: 0;
            color: #333;
        }
        .data {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            word-break: break-all;
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #0056b3;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Debug Admin Authentication State</h1>
        
        <div class="section">
            <h3>Current Storage State</h3>
            <div id="storageState" class="data">Loading...</div>
            <button class="button" onclick="refreshStorageState()">Refresh Storage</button>
            <button class="button" onclick="clearAllStorage()">Clear All Storage</button>
        </div>

        <div class="section">
            <h3>Authentication Test</h3>
            <div id="authTest" class="data">Click test button to check auth</div>
            <button class="button" onclick="testAuthentication()">Test Authentication</button>
        </div>

        <div class="section">
            <h3>Permission Test</h3>
            <div id="permissionTest" class="data">Click test button to check permissions</div>
            <button class="button" onclick="testPermissions()">Test User Management Permissions</button>
        </div>

        <div class="section">
            <h3>Quick Login</h3>
            <button class="button" onclick="loginAsSuperAdmin()">Login as Super Admin</button>
            <button class="button" onclick="loginAsAdmin()">Login as Admin</button>
            <button class="button" onclick="loginAsModerator()">Login as Moderator</button>
            <div id="loginResult" class="data" style="margin-top: 10px;"></div>
        </div>

        <div class="section">
            <h3>Navigation Test</h3>
            <button class="button" onclick="navigateToUsers()">Navigate to /admin/users</button>
            <button class="button" onclick="navigateToAdmins()">Navigate to /admin/users/admins</button>
            <button class="button" onclick="navigateToRoles()">Navigate to /admin/users/roles</button>
        </div>
    </div>

    <script>
        // Admin storage keys
        const ADMIN_STORAGE_KEYS = {
            TOKEN: 'admin_token',
            REFRESH_TOKEN: 'admin_refresh_token',
            USER: 'admin_user',
            SESSION_EXPIRY: 'admin_session_expiry',
            REMEMBER_ME: 'admin_remember_me'
        };

        function refreshStorageState() {
            const storage = {};
            Object.values(ADMIN_STORAGE_KEYS).forEach(key => {
                const value = localStorage.getItem(key);
                storage[key] = value ? JSON.parse(value) : null;
            });

            document.getElementById('storageState').textContent = JSON.stringify(storage, null, 2);
        }

        function clearAllStorage() {
            Object.values(ADMIN_STORAGE_KEYS).forEach(key => {
                localStorage.removeItem(key);
            });
            refreshStorageState();
            document.getElementById('authTest').textContent = 'Storage cleared. Test authentication again.';
            document.getElementById('permissionTest').textContent = 'Storage cleared. Test permissions again.';
        }

        function testAuthentication() {
            const token = localStorage.getItem(ADMIN_STORAGE_KEYS.TOKEN);
            const user = localStorage.getItem(ADMIN_STORAGE_KEYS.USER);
            const sessionExpiry = localStorage.getItem(ADMIN_STORAGE_KEYS.SESSION_EXPIRY);

            let result = 'Authentication Test Results:\n\n';
            
            if (!token) {
                result += '❌ No token found\n';
            } else {
                result += '✅ Token exists\n';
                try {
                    const tokenData = JSON.parse(atob(token.split('.')[1]));
                    result += `Token expires: ${new Date(tokenData.exp * 1000).toLocaleString()}\n`;
                } catch (e) {
                    result += '⚠️ Token format invalid\n';
                }
            }

            if (!user) {
                result += '❌ No user data found\n';
            } else {
                result += '✅ User data exists\n';
                try {
                    const userData = JSON.parse(user);
                    result += `User: ${userData.firstName} ${userData.lastName}\n`;
                    result += `Email: ${userData.email}\n`;
                    result += `Role: ${userData.role}\n`;
                    result += `Active: ${userData.isActive}\n`;
                } catch (e) {
                    result += '⚠️ User data format invalid\n';
                }
            }

            if (sessionExpiry) {
                const expiry = parseInt(sessionExpiry);
                const now = Date.now();
                if (expiry > now) {
                    result += `✅ Session valid until: ${new Date(expiry).toLocaleString()}\n`;
                } else {
                    result += `❌ Session expired at: ${new Date(expiry).toLocaleString()}\n`;
                }
            } else {
                result += '❌ No session expiry found\n';
            }

            document.getElementById('authTest').textContent = result;
        }

        function testPermissions() {
            const user = localStorage.getItem(ADMIN_STORAGE_KEYS.USER);
            
            let result = 'Permission Test Results:\n\n';
            
            if (!user) {
                result += '❌ No user data - cannot test permissions\n';
                document.getElementById('permissionTest').textContent = result;
                return;
            }

            try {
                const userData = JSON.parse(user);
                result += `Testing permissions for: ${userData.role}\n\n`;

                // Test user management permissions
                const rolePermissions = {
                    'super_admin': ['users'],
                    'admin': [],
                    'moderator': [],
                    'viewer': []
                };

                const userResources = rolePermissions[userData.role] || [];
                
                if (userResources.includes('users')) {
                    result += '✅ Has access to user management\n';
                    result += '✅ Can access /admin/users\n';
                    result += '✅ Can access /admin/users/admins\n';
                    result += '✅ Can access /admin/users/roles\n';
                } else {
                    result += '❌ No access to user management\n';
                    result += '❌ Cannot access /admin/users\n';
                    result += '❌ Cannot access /admin/users/admins\n';
                    result += '❌ Cannot access /admin/users/roles\n';
                }

                result += '\nTo access user management:\n';
                result += '1. <NAME_EMAIL>\n';
                result += '2. Password: admin123\n';
                result += '3. Role must be: super_admin\n';

            } catch (e) {
                result += '⚠️ Error parsing user data\n';
            }

            document.getElementById('permissionTest').textContent = result;
        }

        function createMockToken(user, rememberMe = false) {
            const now = Math.floor(Date.now() / 1000);
            const expiry = now + (rememberMe ? 30 * 24 * 60 * 60 : 24 * 60 * 60); // 30 days or 24 hours
            
            const tokenPayload = {
                sub: user.id,
                email: user.email,
                role: user.role,
                type: 'admin',
                iat: now,
                exp: expiry
            };

            return btoa(JSON.stringify({ alg: 'HS256', typ: 'JWT' })) + '.' +
                   btoa(JSON.stringify(tokenPayload)) + '.' +
                   btoa('mock_signature');
        }

        function loginAsSuperAdmin() {
            const user = {
                id: 'admin-1',
                email: '<EMAIL>',
                firstName: 'مدیر',
                lastName: 'سیستم',
                role: 'super_admin',
                isActive: true,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
                twoFactorEnabled: false,
                department: 'مدیریت',
                permissions: []
            };

            const token = createMockToken(user, true);
            const refreshToken = 'mock_refresh_token_' + Date.now();
            const sessionExpiry = Date.now() + (24 * 60 * 60 * 1000); // 24 hours

            localStorage.setItem(ADMIN_STORAGE_KEYS.TOKEN, token);
            localStorage.setItem(ADMIN_STORAGE_KEYS.REFRESH_TOKEN, refreshToken);
            localStorage.setItem(ADMIN_STORAGE_KEYS.USER, JSON.stringify(user));
            localStorage.setItem(ADMIN_STORAGE_KEYS.SESSION_EXPIRY, sessionExpiry.toString());
            localStorage.setItem(ADMIN_STORAGE_KEYS.REMEMBER_ME, 'true');

            document.getElementById('loginResult').innerHTML = '<span class="success">✅ Logged in as Super Admin</span>';
            refreshStorageState();
        }

        function loginAsAdmin() {
            const user = {
                id: 'admin-2',
                email: '<EMAIL>',
                firstName: 'مدیر',
                lastName: 'فروش',
                role: 'admin',
                isActive: true,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
                twoFactorEnabled: false,
                department: 'فروش',
                permissions: []
            };

            const token = createMockToken(user, true);
            const refreshToken = 'mock_refresh_token_' + Date.now();
            const sessionExpiry = Date.now() + (24 * 60 * 60 * 1000);

            localStorage.setItem(ADMIN_STORAGE_KEYS.TOKEN, token);
            localStorage.setItem(ADMIN_STORAGE_KEYS.REFRESH_TOKEN, refreshToken);
            localStorage.setItem(ADMIN_STORAGE_KEYS.USER, JSON.stringify(user));
            localStorage.setItem(ADMIN_STORAGE_KEYS.SESSION_EXPIRY, sessionExpiry.toString());
            localStorage.setItem(ADMIN_STORAGE_KEYS.REMEMBER_ME, 'true');

            document.getElementById('loginResult').innerHTML = '<span class="warning">⚠️ Logged in as Admin (no user management access)</span>';
            refreshStorageState();
        }

        function loginAsModerator() {
            const user = {
                id: 'admin-3',
                email: '<EMAIL>',
                firstName: 'ناظر',
                lastName: 'محتوا',
                role: 'moderator',
                isActive: true,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
                twoFactorEnabled: false,
                department: 'محتوا',
                permissions: []
            };

            const token = createMockToken(user, true);
            const refreshToken = 'mock_refresh_token_' + Date.now();
            const sessionExpiry = Date.now() + (24 * 60 * 60 * 1000);

            localStorage.setItem(ADMIN_STORAGE_KEYS.TOKEN, token);
            localStorage.setItem(ADMIN_STORAGE_KEYS.REFRESH_TOKEN, refreshToken);
            localStorage.setItem(ADMIN_STORAGE_KEYS.USER, JSON.stringify(user));
            localStorage.setItem(ADMIN_STORAGE_KEYS.SESSION_EXPIRY, sessionExpiry.toString());
            localStorage.setItem(ADMIN_STORAGE_KEYS.REMEMBER_ME, 'true');

            document.getElementById('loginResult').innerHTML = '<span class="warning">⚠️ Logged in as Moderator (no user management access)</span>';
            refreshStorageState();
        }

        function navigateToUsers() {
            window.open('http://localhost:5173/admin/users', '_blank');
        }

        function navigateToAdmins() {
            window.open('http://localhost:5173/admin/users/admins', '_blank');
        }

        function navigateToRoles() {
            window.open('http://localhost:5173/admin/users/roles', '_blank');
        }

        // Initialize
        refreshStorageState();
    </script>
</body>
</html>
