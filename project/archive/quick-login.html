<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quick Admin Login</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            max-width: 400px;
            width: 100%;
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .button {
            width: 100%;
            background: #007bff;
            color: white;
            border: none;
            padding: 15px 20px;
            border-radius: 8px;
            cursor: pointer;
            margin: 10px 0;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .button:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }
        .button.success {
            background: #28a745;
        }
        .button.success:hover {
            background: #1e7e34;
        }
        .button.danger {
            background: #dc3545;
        }
        .button.danger:hover {
            background: #c82333;
        }
        .status {
            padding: 15px;
            margin: 15px 0;
            border-radius: 8px;
            font-weight: bold;
            text-align: center;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .links {
            margin-top: 20px;
            text-align: center;
        }
        .links a {
            display: inline-block;
            margin: 5px 10px;
            padding: 10px 15px;
            background: #6c757d;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-size: 14px;
        }
        .links a:hover {
            background: #5a6268;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Quick Admin Login</h1>
        
        <button class="button success" onclick="loginSuperAdmin()">
            🔑 Login as Super Admin
        </button>
        
        <button class="button" onclick="loginAdmin()">
            👤 Login as Admin (No User Access)
        </button>
        
        <button class="button danger" onclick="clearSession()">
            🗑️ Clear Session
        </button>
        
        <div id="status"></div>
        
        <div class="links">
            <a href="http://localhost:5174/admin/dashboard" target="_blank">📊 Dashboard</a>
            <a href="http://localhost:5174/admin/users" target="_blank">👥 Users</a>
            <a href="http://localhost:5174/admin/users/admins" target="_blank">🔧 Admin Users</a>
            <a href="http://localhost:5174/admin/users/roles" target="_blank">🛡️ Roles</a>
        </div>
    </div>

    <script>
        const ADMIN_STORAGE_KEYS = {
            TOKEN: 'admin_token',
            REFRESH_TOKEN: 'admin_refresh_token',
            USER: 'admin_user',
            SESSION_EXPIRY: 'admin_session_expiry',
            REMEMBER_ME: 'admin_remember_me'
        };

        function createMockToken(user, rememberMe = false) {
            const now = Math.floor(Date.now() / 1000);
            const expiry = now + (rememberMe ? 30 * 24 * 60 * 60 : 24 * 60 * 60);
            
            const tokenPayload = {
                sub: user.id,
                email: user.email,
                role: user.role,
                type: 'admin',
                iat: now,
                exp: expiry
            };

            return btoa(JSON.stringify({ alg: 'HS256', typ: 'JWT' })) + '.' +
                   btoa(JSON.stringify(tokenPayload)) + '.' +
                   btoa('mock_signature');
        }

        function setAuthData(user) {
            const token = createMockToken(user, true);
            const refreshToken = 'mock_refresh_token_' + Date.now();
            const sessionExpiry = Date.now() + (24 * 60 * 60 * 1000);

            localStorage.setItem(ADMIN_STORAGE_KEYS.TOKEN, token);
            localStorage.setItem(ADMIN_STORAGE_KEYS.REFRESH_TOKEN, refreshToken);
            localStorage.setItem(ADMIN_STORAGE_KEYS.USER, JSON.stringify(user));
            localStorage.setItem(ADMIN_STORAGE_KEYS.SESSION_EXPIRY, sessionExpiry.toString());
            localStorage.setItem(ADMIN_STORAGE_KEYS.REMEMBER_ME, 'true');
        }

        function loginSuperAdmin() {
            const user = {
                id: 'admin-1',
                email: '<EMAIL>',
                firstName: 'مدیر',
                lastName: 'سیستم',
                role: 'super_admin',
                isActive: true,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
                twoFactorEnabled: false,
                department: 'مدیریت',
                permissions: [
                    {
                        resource: 'products',
                        actions: ['create', 'read', 'update', 'delete', 'export', 'import']
                    },
                    {
                        resource: 'orders',
                        actions: ['create', 'read', 'update', 'delete', 'export']
                    },
                    {
                        resource: 'customers',
                        actions: ['create', 'read', 'update', 'delete', 'export']
                    },
                    {
                        resource: 'reviews',
                        actions: ['read', 'update', 'delete', 'approve', 'reject', 'moderate', 'export']
                    },
                    {
                        resource: 'loyalty',
                        actions: ['create', 'read', 'update', 'delete', 'configure']
                    },
                    {
                        resource: 'content',
                        actions: ['create', 'read', 'update', 'delete']
                    },
                    {
                        resource: 'analytics',
                        actions: ['read', 'export']
                    },
                    {
                        resource: 'settings',
                        actions: ['create', 'read', 'update', 'delete']
                    },
                    {
                        resource: 'users',
                        actions: ['create', 'read', 'update', 'delete']
                    },
                    {
                        resource: 'audit',
                        actions: ['read', 'export']
                    },
                    {
                        resource: 'notifications',
                        actions: ['create', 'read', 'update', 'delete']
                    }
                ]
            };

            setAuthData(user);
            
            document.getElementById('status').innerHTML = 
                '<div class="status success">✅ Successfully logged in as Super Admin!<br>You now have full access to user management.</div>';
        }

        function loginAdmin() {
            const user = {
                id: 'admin-2',
                email: '<EMAIL>',
                firstName: 'مدیر',
                lastName: 'فروش',
                role: 'admin',
                isActive: true,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
                twoFactorEnabled: false,
                department: 'فروش',
                permissions: [
                    {
                        resource: 'products',
                        actions: ['create', 'read', 'update', 'delete', 'export', 'import']
                    },
                    {
                        resource: 'orders',
                        actions: ['read', 'update', 'export']
                    },
                    {
                        resource: 'customers',
                        actions: ['read', 'update', 'export']
                    },
                    {
                        resource: 'reviews',
                        actions: ['read', 'update', 'approve', 'reject', 'moderate', 'export']
                    },
                    {
                        resource: 'loyalty',
                        actions: ['read', 'update']
                    },
                    {
                        resource: 'content',
                        actions: ['create', 'read', 'update', 'delete']
                    },
                    {
                        resource: 'analytics',
                        actions: ['read', 'export']
                    },
                    {
                        resource: 'settings',
                        actions: ['read']
                    },
                    {
                        resource: 'notifications',
                        actions: ['read', 'create', 'update']
                    }
                ]
            };

            setAuthData(user);
            
            document.getElementById('status').innerHTML = 
                '<div class="status warning">⚠️ Logged in as Admin - NO access to user management.<br>Try accessing user pages to see access denied.</div>';
        }

        function clearSession() {
            Object.values(ADMIN_STORAGE_KEYS).forEach(key => {
                localStorage.removeItem(key);
            });
            
            document.getElementById('status').innerHTML = 
                '<div class="status error">🗑️ Session cleared. You are now logged out.</div>';
        }

        // Check current status on load
        window.onload = function() {
            const user = localStorage.getItem(ADMIN_STORAGE_KEYS.USER);
            if (user) {
                try {
                    const userData = JSON.parse(user);
                    document.getElementById('status').innerHTML = 
                        `<div class="status success">Current user: ${userData.firstName} ${userData.lastName} (${userData.role})</div>`;
                } catch (e) {
                    document.getElementById('status').innerHTML = 
                        '<div class="status error">Invalid session data found.</div>';
                }
            } else {
                document.getElementById('status').innerHTML = 
                    '<div class="status warning">No active session. Please login.</div>';
            }
        };
    </script>
</body>
</html>
